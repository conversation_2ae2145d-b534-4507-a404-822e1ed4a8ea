# 使用 Ubuntu 22.04 作为基础镜像
FROM ubuntu:22.04

# 避免在构建过程中出现交互式提示
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 更新系统并安装必要的依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    sqlite3 \
    libsqlite3-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建数据库目录
RUN mkdir -p /app/database

# 配置 pip 使用清华大学源
RUN mkdir -p /root/.pip
RUN echo "[global]\nindex-url = https://pypi.tuna.tsinghua.edu.cn/simple" > /root/.pip/pip.conf

# 复制项目文件
COPY requirements.txt .
COPY app.py .
COPY db_models.py .
COPY db_operator.py .
COPY openaiSDK.py .
COPY wsgi.py .

# 复制 assets 目录
COPY assets /app/assets
COPY callbacks /app/callbacks
COPY components /app/components
COPY styles /app/styles
COPY utils /app/utils

# 安装 Python 依赖
RUN pip3 install --no-cache-dir -r requirements.txt

# 安装 Gunicorn
RUN pip3 install gunicorn

# 暴露端口
EXPOSE 8050

# 设置环境变量
ENV PYTHONUNBUFFERED=1

# 使用 Gunicorn 运行应用
CMD ["gunicorn", "--bind", "0.0.0.0:8050", "--workers", "8", "--timeout", "300", "wsgi:server"]