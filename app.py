#app.py
import logging
import dash
import dash_bootstrap_components as dbc
# 导入其他模块
from components.layout import create_app_layout
from callbacks.auth_callbacks import register_auth_callbacks
from callbacks.model_callbacks import register_model_callbacks
from callbacks.conversation_callbacks import register_conversation_callbacks
from callbacks.message_callbacks import register_message_callbacks
from callbacks.admin_callbacks import register_admin_callbacks
from callbacks.image_callbacks import register_image_callbacks
from callbacks.settings_callbacks import register_settings_callbacks
from callbacks.modal_dialog_callbacks import register_modal_dailog_callbacks


# 初始化日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 初始化应用
app = dash.Dash(__name__,
                external_stylesheets=[
                    dbc.themes.JOURNAL,
                    "https://use.fontawesome.com/releases/v5.15.1/css/all.css",
                    "/assets/custom.css"
                ],
                suppress_callback_exceptions=True,
                assets_folder='assets')

app.title = "InspirFlow"
# 设置应用布局
app.layout = create_app_layout()

# 注册回调
register_auth_callbacks(app)
register_model_callbacks(app)
register_conversation_callbacks(app)
register_message_callbacks(app)
register_image_callbacks(app)
register_settings_callbacks(app)
register_admin_callbacks(app)
register_modal_dailog_callbacks(app)

server = app.server

# 应用启动
if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=8050)
