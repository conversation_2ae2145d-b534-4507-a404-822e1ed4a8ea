// assets/clientside.js 修改版
if (!window.dash_clientside) {
    window.dash_clientside = {};
}

window.dash_clientside.clientside = {
    // 处理响应式布局
    handleResponsiveLayout: function() {
        // 添加视口元标记确保移动设备正确缩放
        if (!document.getElementById('viewport-meta-tag')) {
            var meta = document.createElement('meta');
            meta.id = 'viewport-meta-tag';
            meta.name = 'viewport';
            meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0';
            document.getElementsByTagName('head')[0].appendChild(meta);
        }

        // 检测是否为移动设备
        function isMobileDevice() {
            return (window.innerWidth <= 992) ||
                   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        // 在DOM加载完成后执行
        function setupResponsiveness() {
            // 监听窗口大小变化
            window.addEventListener('resize', adjustLayout);

            // 立即执行一次布局调整
            adjustLayout();

            // 同步桌面和移动控件
            setupControlSync();
        }

        // 根据屏幕宽度调整布局
        function adjustLayout() {
            const isMobile = window.innerWidth <= 992;

            // 调整主要行的布局
            const mainRow = document.getElementById('main-layout-row');
            if (mainRow) {
                if (isMobile) {
                    mainRow.classList.add('flex-column');
                } else {
                    mainRow.classList.remove('flex-column');
                }
            }

            // 隐藏/显示管理员面板
            const adminPanel = document.getElementById('admin-panel');
            if (adminPanel) {
                if (isMobile) {
                    adminPanel.style.display = 'none';
                } else {
                    // 恢复服务器端设置的显示状态，如果管理员已登录
                    if (adminPanel.getAttribute('data-visible') === 'true') {
                        adminPanel.style.display = 'block';
                    }
                }
            }
        }

        // 同步桌面和移动版控件
        function setupControlSync() {
            // 同步两个"新建对话"按钮
            syncButtons('create-conversation-btn', 'create-conversation-btn-mobile');
            syncButtons('delete-selected-btn', 'delete-selected-btn-mobile');

            // 监听并同步模型选择器状态
            syncSelectorsState('model-selector', 'model-selector-mobile');

            // 监听并同步温度滑块状态
            syncSlidersState('temperature-slider', 'temperature-slider-mobile');

            // 确保对话列表不会重复
            preventListDuplication('conversations-list', 'conversations-list-mobile');

            // 同步用户统计
            syncContent('user-stats', 'user-stats-mobile');
        }

        // 同步两个按钮的状态和点击事件
        function syncButtons(desktop, mobile) {
            const desktopBtn = document.getElementById(desktop);
            const mobileBtn = document.getElementById(mobile);

            if (desktopBtn && mobileBtn) {
                // 同步禁用状态
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'disabled') {
                            const isDisabled = desktopBtn.disabled;
                            mobileBtn.disabled = isDisabled;
                        }
                    });
                });
                observer.observe(desktopBtn, { attributes: true });

                // 当移动版按钮被点击时, 触发桌面版按钮的点击事件
                mobileBtn.addEventListener('click', function() {
                    desktopBtn.click();
                });
            }
        }

        // 同步选择器的禁用/启用状态（不同步值，值通过回调同步）
        function syncSelectorsState(desktop, mobile) {
            const desktopSelector = document.getElementById(desktop);
            const mobileSelector = document.getElementById(mobile);

            if (desktopSelector && mobileSelector) {
                // 观察禁用状态变化
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'disabled') {
                            const isDisabled = desktopSelector.disabled;
                            mobileSelector.disabled = isDisabled;
                        }
                    });
                });
                observer.observe(desktopSelector, { attributes: true });
            }
        }

        // 同步滑块的禁用/启用状态（不同步值，值通过回调同步）
        function syncSlidersState(desktop, mobile) {
            const desktopSlider = document.getElementById(desktop);
            const mobileSlider = document.getElementById(mobile);

            if (desktopSlider && mobileSlider) {
                // 观察禁用状态变化
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'disabled') {
                            const isDisabled = desktopSlider.disabled;
                            mobileSlider.disabled = isDisabled;
                        }
                    });
                });
                observer.observe(desktopSlider, { attributes: true });
            }
        }

        // 防止移动端列表重复显示桌面端的内容
        function preventListDuplication(desktop, mobile) {
            const desktopList = document.getElementById(desktop);
            const mobileList = document.getElementById(mobile);

            if (desktopList && mobileList) {
                // 确保移动版列表项目有唯一的ID
                const ensureUniqueIds = function() {
                    if (mobileList.children.length > 0) {
                        Array.from(mobileList.children).forEach((item, index) => {
                            // 只给没有ID的元素添加ID
                            if (!item.id) {
                                item.id = `mobile-item-${index}`;
                            }

                            // 确保点击事件正常处理
                            item.addEventListener('click', function(e) {
                                // 查找匹配的桌面项目并触发点击
                                const matchingDesktopItem = Array.from(desktopList.children)
                                    .find(dItem => dItem.textContent === this.textContent);
                                if (matchingDesktopItem) {
                                    matchingDesktopItem.click();
                                }
                                e.stopPropagation(); // 阻止事件冒泡
                            });
                        });
                    }
                };

                // 在DOM变化时确保唯一ID
                const observer = new MutationObserver(ensureUniqueIds);
                observer.observe(mobileList, { childList: true, subtree: true });

                // 初始执行一次
                ensureUniqueIds();
            }
        }

        // 同步内容区域
        function syncContent(desktop, mobile) {
            const desktopContent = document.getElementById(desktop);
            const mobileContent = document.getElementById(mobile);

            if (desktopContent && mobileContent) {
                const observer = new MutationObserver(function(mutations) {
                    // 仅当内容确实有变化时才同步
                    if (desktopContent.innerHTML !== mobileContent.innerHTML) {
                        mobileContent.innerHTML = desktopContent.innerHTML;
                    }
                });
                observer.observe(desktopContent, { childList: true, subtree: true });

                // 初始同步
                if (desktopContent.innerHTML && mobileContent.innerHTML !== desktopContent.innerHTML) {
                    mobileContent.innerHTML = desktopContent.innerHTML;
                }
            }
        }

        // 当DOM加载完成后设置响应式处理
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupResponsiveness);
        } else {
            setupResponsiveness();
        }

        return '';
    },

    // 处理消息更新事件
    refreshMessagesOnEvent: function(eventData) {
        // 此函数会在接收到事件时触发
        // 它的主要作用是返回一个值以触发服务器端回调
        if (eventData) {
            return "refresh-" + eventData.timestamp;
        }
        return "no-refresh";
    }
};

// assets/custom.js

if (!window.dashExtensions) {
    window.dashExtensions = {};
}

// 上传进度模拟和管理
window.dashExtensions.uploadProgress = function() {
    // 清除之前可能存在的定时器
    if (window.uploadProgressInterval) {
        clearInterval(window.uploadProgressInterval);
    }

    let progress = 0;
    const progressBar = document.getElementById('upload-progress');
    const statusText = document.getElementById('upload-status-text');

    if (progressBar && statusText) {
        // 重置进度条状态
        progressBar.setAttribute('value', 0);
        progressBar.setAttribute('aria-valuenow', 0);
        statusText.textContent = "准备上传...";
        statusText.className = "text-muted small";

        // 创建新的定时器并保存引用
        window.uploadProgressInterval = setInterval(() => {
            // 进度增长速度随进度变化
            let increment = 0;
            if (progress < 30) {
                increment = 5 + Math.random() * 10; // 开始快一些
            } else if (progress < 70) {
                increment = 2 + Math.random() * 5;  // 中间慢一些
            } else if (progress < 90) {
                increment = 0.5 + Math.random() * 2; // 接近结束更慢
            }

            progress += increment;
            if (progress > 95) {
                clearInterval(window.uploadProgressInterval);
                progress = 95; // 最后5%留给服务器处理
            }

            // 更新进度条和文本
            progressBar.setAttribute('value', progress);
            progressBar.setAttribute('aria-valuenow', progress);
            statusText.textContent = `上传中... ${Math.round(progress)}%`;

            // 更新进度条颜色
            if (progress < 30) {
                progressBar.className = "progress-bar progress-bar-striped progress-bar-animated bg-info";
            } else if (progress < 70) {
                progressBar.className = "progress-bar progress-bar-striped progress-bar-animated bg-primary";
            } else {
                progressBar.className = "progress-bar progress-bar-striped progress-bar-animated bg-success";
            }
        }, 100);

        // 安全措施：10秒后如果还没完成，清除定时器
        setTimeout(() => {
            if (window.uploadProgressInterval) {
                clearInterval(window.uploadProgressInterval);
                if (parseInt(progressBar.getAttribute('aria-valuenow')) < 100) {
                    progressBar.setAttribute('value', 95);
                    progressBar.setAttribute('aria-valuenow', 95);
                    statusText.textContent = "等待服务器处理...";
                }
            }
        }, 10000);
    }

    return '';
};

// 完成上传
window.dashExtensions.uploadComplete = function() {
    if (window.uploadProgressInterval) {
        clearInterval(window.uploadProgressInterval);
    }

    const progressBar = document.getElementById('upload-progress');
    const statusText = document.getElementById('upload-status-text');

    if (progressBar && statusText) {
        progressBar.setAttribute('value', 100);
        progressBar.setAttribute('aria-valuenow', 100);
        progressBar.className = "progress-bar bg-success";
        statusText.textContent = "上传完成";
        statusText.className = "text-success small";
    }

    return '';
};

// 上传失败
window.dashExtensions.uploadFailed = function(errorMsg) {
    if (window.uploadProgressInterval) {
        clearInterval(window.uploadProgressInterval);
    }

    const progressBar = document.getElementById('upload-progress');
    const statusText = document.getElementById('upload-status-text');

    if (progressBar && statusText) {
        progressBar.setAttribute('value', 100);
        progressBar.setAttribute('aria-valuenow', 100);
        progressBar.className = "progress-bar bg-danger";
        statusText.textContent = errorMsg || "上传失败";
        statusText.className = "text-danger small";
    }

    return '';
};

// 优化图片预览
window.dashExtensions.optimizeImagePreview = function() {
    // 为所有预览图片添加懒加载和渐进式加载
    const previewImages = document.querySelectorAll('#image-preview-area img');
    previewImages.forEach(img => {
        // 添加loading属性
        img.setAttribute('loading', 'lazy');

        // 添加模糊效果，然后逐渐清晰
        img.style.filter = 'blur(5px)';
        img.style.transition = 'filter 0.5s ease-in-out';

        img.onload = function() {
            this.style.filter = 'blur(0)';
        };

        // 设置低质量的占位符
        if (!img.getAttribute('data-src') && img.src) {
            img.setAttribute('data-src', img.src);
            // 可以在这里设置一个低质量的占位符，但这需要服务器支持
        }
    });

    return '';
};
