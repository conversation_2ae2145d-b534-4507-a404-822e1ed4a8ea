/* custom.css */
.text-danger {
    color: #dc3545;  /* 或您想要的危险色 */
}
/* -------------- 1. 变量与全局设置 -------------- */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --background-color: #f4f6f7;
    --text-color: #2c3e50;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    line-height: 1.6;
    color: var(--text-color);
}

/* -------------- 2. 通用组件样式 -------------- */
/* 卡片样式 */
.card {
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
}

/* 表格样式 */
table {
    border-collapse: collapse;
    width: 100%;
    margin: 15px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

/* -------------- 3. 布局相关样式 -------------- */
/* 流动布局过渡效果 */
.content-row, .mobile-column, .desktop-column {
    transition: all 0.3s ease;
}

/* -------------- 4. 对话列表组件 -------------- */
/* 对话列表容器 */
#conversations-list, #conversations-list-mobile {
    display: flex;
    flex-direction: column;
    gap: 4px;
    max-height: 200px;
    height: 150px;
    overflow-y: auto;
    padding-right: 2px;
    border-left: none; /* 确保没有左侧边框 */
}

/* 对话列表滚动条 */
#conversations-list::-webkit-scrollbar {
    width: 6px;
}

#conversations-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

#conversations-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

#conversations-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* 对话项容器 */
.conversation-item {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.8);
    border-left: 3px solid transparent; /* 默认透明边框 */
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 2px;
    min-height: 28px;
}

.conversation-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* 复选框容器 */
.conversation-select-container {
    margin-right: 6px;
    display: flex;
    align-items: center;
    height: 18px;
}

.small-checkbox {
    transform: scale(0.7);
    margin: 0;
}


/* 会话按钮 */
.conversation-button {
    min-height: 0; /* 覆盖Bootstrap的最小高度 */
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    color: red !important; /* 使用继承的颜色 */
    width: 100%;
    text-align: left;
    padding: 0 !important;
    margin: 0 !important;
    flex: 1; /* 让按钮占据除复选框外的所有空间 */
}

/* 确保按钮内容左对齐 */
.conversation-button > div {
    text-align: left;
    width: 100%;
}

.conversation-content {
    width: 100%;
    min-width: 0; /* 确保内容可以收缩 */
    color: red;
}

/* 标题样式 */
.conversation-title {
    font-weight: 500;
    margin-bottom: 1px;
    font-size: 0.9rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversation-title-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 300px;
}

/* 对话时间和预览 */
.conversation-time {
    font-size: 0.7rem;
    color: #95a5a6;
}

.conversation-preview {
    font-size: 0.8rem;
    color: #7f8c8d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    line-height: 1.3;
    margin-top: 1px;
}

/* 选中状态 */
.selected-conversation {
    background-color: rgba(219, 52, 69, 0.1); /* 调整为偏红的颜色，并稍微增加不透明度 */
    border-left-color: #990000; /* 更深的红色，你可以根据需要调整 */
}

/* -------------- 5. 消息组件样式 -------------- */
/* 消息容器 */
.message-container {
    display: flex;
    flex-direction: row;
    margin-bottom: 15px;
    transition: background-color 0.2s;
    border-radius: 10px;
    padding: 10px;
    position: relative;
    overflow: visible;
    height: auto;
}

.message-container:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 消息头像 */
.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: rgba(0, 0, 0, 0.05);
}

/* 消息内容 */
.message-content {
    flex: 1;
    position: relative;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    background-color: white;
    height: auto;
    overflow: visible;
}

.message-content-inner {
    padding: 15px;
    max-height: none;
    overflow: visible;
}

/* 消息操作按钮 */
.message-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255,255,255,0.9);
    border-radius: 6px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 10;
}

.message-content:hover .message-actions {
    opacity: 1;
}

/* 消息元数据 */
.message-metadata {
    font-size: 0.75rem;
    color: #95a5a6;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 5px;
}

.message-metadata:before {
    content: "\2139\FE0F";
    font-style: normal;
}

/* 消息图片 */
.message-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 5px 0;
    border: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#image-upload-status {
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 消息内容区适配 */
.message-content-wrapper img,
.message-content-wrapper table {
    max-width: 100%;
    height: auto;
}

/* -------------- 6. Markdown渲染样式 -------------- */
/* iframe 样式优化 */
.rendered-markdown-frame {
    width: 100%;
    transition: height 0.2s ease-in-out;
    height: auto;
    border: none;
    overflow: visible;
    display: block;
}

/* 代码块样式 */
.codehilite {
    background: #f8f8f8;
    border-radius: 4px;
    padding: 1em;
    margin: 1em 0;
    overflow: auto;
    border: 1px solid #e0e0e0;
}

.codehilite pre {
    margin: 0;
}

/* 数学公式容器 */
.arithmatex {
    overflow-x: auto;
}

/* -------------- 7. 响应式布局设置 -------------- */
/* 桌面设备布局 (大于等于993px宽) */
@media (min-width: 993px) {
    .mobile-only {
        display: none !important;
    }

    /* 桌面特定样式可以在这里添加 */
}

/* 平板与移动设备布局 (小于等于992px宽) */
@media (max-width: 992px) {
    /* 移动设备布局调整 */
    .desktop-only {
        display: none !important;
    }

    .mobile-column {
        width: 100% !important;
        margin-bottom: 15px;
    }

    .content-row {
        flex-direction: column !important;
    }

    /* 对话列表优化 */
    #conversations-list {
        max-height: 250px !important;
        margin-bottom: 15px;
    }

    /* 更紧凑的卡片间距 */
    .card {
        margin-bottom: 10px;
    }

    /* 导航栏优化 */
    .navbar-brand {
        font-size: 1.2rem;
    }

    /* 消息容器高度调整 */
    #messages-container {
        height: 400px !important;
    }

    /* 主要内容区域内边距调整 */
    #main-content {
        padding: 0 10px;
    }

    /* 调整消息输入区域 */
    .message-input-col {
        width: 75% !important;
    }

    .message-action-col {
        width: 25% !important;
    }
}

/* 小屏手机设备 (小于等于768px宽) */
@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }

    .message-container {
        flex-direction: column;
        align-items: flex-start;
    }

    /* 其他小屏幕手机特定样式 */
}



