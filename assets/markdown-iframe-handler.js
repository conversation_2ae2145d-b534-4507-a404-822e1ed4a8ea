// markdown-iframe-handler.js - 防止高度累加版本
document.addEventListener('DOMContentLoaded', function() {
    // 存储iframe的实际内容高度（不含padding）
    const iframeBaseHeights = {};

    // 处理来自iframe的高度消息
    function handleIframeMessage(e) {
        if (e.data && typeof e.data.height === 'number' && e.data.iframe_id) {
            const iframeId = e.data.iframe_id;
            const iframe = document.getElementById(iframeId);

            if (iframe) {
                // 存储基础高度（不含padding）
                const contentHeight = Math.max(30, e.data.height);
                iframeBaseHeights[iframeId] = contentHeight;

                // 应用固定的padding（不累加）
                const padding = 15;
                iframe.style.height = (contentHeight + padding) + 'px';
            }
        }
    }

    // 监听iframe消息
    window.addEventListener('message', handleIframeMessage);

    // 简化的调整请求 - 减少频率
    let resizeTimeout = null;

    function requestIframesResize() {
        // 清除任何待处理的调整
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
            resizeTimeout = null;
        }

        // 执行调整
        document.querySelectorAll('.rendered-markdown-frame').forEach(iframe => {
            if (iframe.contentWindow) {
                try {
                    iframe.contentWindow.postMessage({
                        action: 'resize',
                        iframe_id: iframe.id
                    }, '*');
                } catch (e) {}
            }
        });
    }

    // 限制调整的频率
    function throttledRequestResize() {
        if (!resizeTimeout) {
            resizeTimeout = setTimeout(() => {
                requestIframesResize();
                resizeTimeout = null;
            }, 200);
        }
    }

    // 简化的监听设置 - 减少监听器数量
    function setupResizeSystem() {
        // 窗口大小变化
        window.addEventListener('resize', throttledRequestResize);

        // 内容可能变化时的检查点
        setTimeout(requestIframesResize, 1000);
    }

    // 启动系统
    setupResizeSystem();
});
