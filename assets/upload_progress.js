// assets/upload_progress.js

// 监听文件上传事件
document.addEventListener('DOMContentLoaded', function() {
    // 定期检查上传组件是否已加载
    const checkInterval = setInterval(function() {
        const uploadElement = document.getElementById('upload-image');
        if (uploadElement) {
            clearInterval(checkInterval);
            setupUploadListener(uploadElement);
        }
    }, 500);

    function setupUploadListener(uploadElement) {
        // 监听上传组件的变化
        uploadElement.addEventListener('change', function(e) {
            const files = e.target.files;
            if (!files || files.length === 0) return;

            const file = files[0];
            const statusDiv = document.getElementById('image-upload-status');

            if (!statusDiv) return;

            // 创建进度显示元素
            const sizeKB = file.size / 1024;
            const sizeText = sizeKB > 1024 ? `${(sizeKB/1024).toFixed(1)} MB` : `${sizeKB.toFixed(1)} KB`;

            statusDiv.innerHTML = `
                <div class="small text-muted">处理图片: ${file.name} (${sizeText})</div>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%" 
                         aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            `;

            // 对于大文件，显示处理进度
            if (file.size > 1024 * 1024) { // 大于1MB的文件
                let progress = 0;
                const progressBar = statusDiv.querySelector('.progress-bar');

                // 模拟处理进度 - 由于Dash的上传机制，我们无法获取真实的上传进度
                // 但可以根据文件大小调整进度增长速度
                const interval = setInterval(() => {
                    // 文件越大，进度增长越慢
                    const increment = Math.max(0.5, 5 - (file.size / (1024 * 1024)));
                    progress += increment;

                    if (progress >= 95) {
                        clearInterval(interval);
                        progress = 95; // 保留最后5%给服务器处理
                    }

                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                }, 100);

                // 10秒后如果还没完成，清除定时器
                setTimeout(() => {
                    clearInterval(interval);
                }, 10000);
            } else {
                // 小文件直接显示处理中
                const progressBar = statusDiv.querySelector('.progress-bar');
                progressBar.style.width = '90%';
                progressBar.setAttribute('aria-valuenow', 90);
            }
        });
    }
});
