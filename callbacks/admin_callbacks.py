#admin_callbacks.py
import dash
from dash import Input, Output, State, ALL, html
from dash.exceptions import PreventUpdate
import dash_bootstrap_components as dbc
import json
import logging

from db_operator import (
    get_all_users,
    toggle_user_active_status,
    admin_add_user_balance,
    admin_create_new_user
)

# 初始化日志
logger = logging.getLogger(__name__)

def register_admin_callbacks(app):
    # 加载用户列表
    @app.callback(
        Output("users-table-container", "children"),
        [Input("refresh-users-btn", "n_clicks"),
         Input("admin-panel", "style"),
         Input("close-api-key-modal", "n_clicks")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def load_users_table(n_clicks, admin_panel_style, close_modal_clicks, admin_id):
        if not admin_id:
            raise PreventUpdate

        # 获取所有用户
        users = get_all_users(admin_id)
        if not users:
            return html.Div("您没有权限查看用户列表", className="text-danger")

        # 创建用户表格
        table_header = [
            html.Thead(html.Tr([
                html.Th("ID"),
                html.Th("API Key (部分)"),
                html.Th("权限"),
                html.Th("状态"),
                html.Th("余额($)"),
                html.Th("总消费($)"),
                html.Th("总充值($)"),
                html.Th("创建时间"),
                html.Th("操作")
            ]))
        ]

        rows = []
        for user in users:
            # 确定用户状态显示
            status_badge = dbc.Badge("活跃", color="success") if user["is_active"] else dbc.Badge("已禁用", color="danger")

            # 根据权限显示不同样式
            permission_text = "管理员" if user["permission"] == 9 else "普通用户"
            permission_badge = dbc.Badge(permission_text,
                                         color="primary" if user["permission"] == 9 else "info")

            # 操作按钮
            action_buttons = []
            # 管理员不能禁用自己
            if user["id"] != admin_id:
                button_text = "禁用" if user["is_active"] else "启用"
                button_color = "warning" if user["is_active"] else "success"
                action_buttons.append(
                    dbc.Button(
                        button_text,
                        id={"type": "toggle-user-btn", "index": user["id"]},
                        color=button_color,
                        size="sm",
                        className="me-2"
                    )
                )

            # 充值按钮
            action_buttons.append(
                dbc.Button(
                    "充值",
                    id={"type": "quick-deposit-btn", "index": user["id"]},
                    color="primary",
                    size="sm"
                )
            )

            row = html.Tr([
                html.Td(user["id"]),
                html.Td(user["api_key"]),
                html.Td(permission_badge),
                html.Td(status_badge),
                html.Td(f"{user['current_balance']:.4f}"),
                html.Td(f"{user['total_spent']:.4f}"),
                html.Td(f"{user['total_deposited']:.4f}"),
                html.Td(user["created_at"].split("T")[0] if user["created_at"] else ""),
                html.Td(html.Div(action_buttons, className="d-flex"))
            ])
            rows.append(row)

        table_body = [html.Tbody(rows)]

        return dbc.Table(
            table_header + table_body,
            bordered=True,
            hover=True,
            responsive=True,
            striped=True,
            className="mt-3"
        )

    # 切换用户活跃状态
    @app.callback(
        Output("refresh-users-btn", "n_clicks", allow_duplicate=True),
        [Input({"type": "toggle-user-btn", "index": ALL}, "n_clicks")],
        [State({"type": "toggle-user-btn", "index": ALL}, "id"),
         State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def toggle_user_status(n_clicks_list, btn_ids, admin_id):
        if not n_clicks_list or not any(n_clicks_list) or not admin_id:
            raise PreventUpdate

        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        button_id = ctx.triggered[0]["prop_id"].split(".")[0]
        user_id = json.loads(button_id)["index"]

        # 切换用户状态
        success = toggle_user_active_status(admin_id, user_id)

        # 模拟点击刷新按钮来重新加载用户表格
        return 1

    # 显示添加用户模态窗口
    @app.callback(
        Output("add-user-modal", "is_open"),
        [Input("add-user-btn", "n_clicks"),
         Input("cancel-add-user", "n_clicks"),
         Input("close-api-key-modal", "n_clicks")],
        [State("add-user-modal", "is_open")],
        prevent_initial_call=True
    )
    def toggle_add_user_modal(add_clicks, cancel_clicks, close_api_modal_clicks, is_open):
        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        button_id = ctx.triggered[0]["prop_id"].split(".")[0]
        if button_id == "add-user-btn":
            return True
        elif button_id == "cancel-add-user" or button_id == "close-api-key-modal":
            return False
        return is_open

    # 创建新用户
    @app.callback(
        [Output("add-user-status", "children"),
         Output("api-key-display-modal", "is_open", allow_duplicate=True),
         Output("new-user-api-key", "children"),
         Output("add-user-modal", "is_open", allow_duplicate=True)],
        [Input("create-user", "n_clicks")],
        [State("new-user-permission", "value"),
         State("new-user-balance", "value"),
         State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def create_new_user(n_clicks, permission, initial_balance, admin_id):
        if not n_clicks or not admin_id:
            raise PreventUpdate

        try:
            # 创建新用户
            result = admin_create_new_user(admin_id, permission, initial_balance)

            if not result:
                return dbc.Alert("创建用户失败: 没有权限", color="danger"), False, "", False

            user_data, api_key = result

            return dbc.Alert("用户创建成功", color="success"), True, api_key, False
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            return dbc.Alert(f"创建用户失败: {str(e)}", color="danger"), False, "", True

    # 加载充值页面的用户下拉列表
    @app.callback(
        [Output("deposit-user-selector", "options"),
         Output("deposit-user-selector", "value")],
        [Input("admin-tabs", "active_tab")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def load_deposit_user_selector(active_tab, admin_id):
        if active_tab != "financial-management" or not admin_id:
            raise PreventUpdate

        # 获取所有用户
        users = get_all_users(admin_id)
        if not users:
            return [], None

        options = [
            {"label": f"ID: {u['id']} - 余额: ${u['current_balance']:.2f} - API Key: {u['api_key']}", "value": u["id"]}
            for u in users if u["is_active"]]

        return options, None

    # 处理快速充值按钮
    @app.callback(
        [Output("deposit-user-selector", "value", allow_duplicate=True),
         Output("admin-tabs", "active_tab")],
        [Input({"type": "quick-deposit-btn", "index": ALL}, "n_clicks")],
        [State({"type": "quick-deposit-btn", "index": ALL}, "id")],
        prevent_initial_call=True
    )
    def handle_quick_deposit(n_clicks_list, btn_ids):
        if not n_clicks_list or not any(n_clicks_list):
            raise PreventUpdate

        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        button_id = ctx.triggered[0]["prop_id"].split(".")[0]
        user_id = json.loads(button_id)["index"]

        # 切换到充值选项卡并选中该用户
        return user_id, "financial-management"

    # 处理用户充值
    @app.callback(
        Output("deposit-status", "children"),
        [Input("confirm-deposit-btn", "n_clicks")],
        [State("deposit-user-selector", "value"),
         State("deposit-amount-input", "value"),
         State("deposit-description-input", "value"),
         State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def handle_user_deposit(n_clicks, user_id, amount, description, admin_id):
        if not n_clicks or not user_id or not amount or not admin_id:
            raise PreventUpdate

        try:
            # 确认金额为正数
            if amount <= 0:
                return dbc.Alert("充值金额必须大于零", color="danger")

            # 充值
            success = admin_add_user_balance(admin_id, user_id, amount, description)

            if success:
                return dbc.Alert(f"已成功为用户 {user_id} 充值 ${amount}", color="success")
            else:
                return dbc.Alert("充值失败", color="danger")
        except Exception as e:
            logger.error(f"用户充值失败: {str(e)}")
            return dbc.Alert(f"充值失败: {str(e)}", color="danger")

    # 更新管理员统计卡片
    @app.callback(
        [Output("total-users-count", "children"),
         Output("active-users-count", "children"),
         Output("total-transaction-amount", "children")],
        [Input("admin-tabs", "active_tab"),
         Input("user-stats-refresh", "n_intervals")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def update_admin_statistics(active_tab, n_intervals, admin_id):
        if not admin_id:
            raise PreventUpdate

        # 获取所有用户
        users = get_all_users(admin_id)
        if not users:
            return "N/A", "N/A", "N/A"

        total_users = len(users)
        active_users = sum(1 for user in users if user["is_active"])

        # 计算总交易金额
        total_deposits = sum(user["total_deposited"] for user in users)

        return total_users, active_users, f"${total_deposits:.2f}"