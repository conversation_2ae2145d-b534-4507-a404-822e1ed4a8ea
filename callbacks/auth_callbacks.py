# auth_callbacks.py文件用于处理用户登录验证和JWT令牌创建
import logging
from dash import Input, Output, State
from dash.exceptions import PreventUpdate
import dash_bootstrap_components as dbc
from db_operator import verify_api_key
from utils.auth import create_access_token
# 初始化日志
logger = logging.getLogger(__name__)


def register_auth_callbacks(app):
    @app.callback(
        [Output("api-key-status", "children"),
         Output("main-content", "style"),
         Output("api-key-container", "style"),
         Output("store-user-apikey", "data"),
         Output("store-user-id", "data"),
         Output("admin-panel", "style", allow_duplicate=True),
         Output("store-user-permission", "data", allow_duplicate=True)],
        [Input("verify-api-key", "n_clicks")],
        [State("api-key-input", "value")],
        prevent_initial_call=True
    )
    def verify_api_key_callback(n_clicks, api_key):
        if not n_clicks:
            raise PreventUpdate

        if not api_key:
            return dbc.Alert("请输入API Key", color="danger"), {"display": "none"}, {}, None, None, {
                "display": "none"}, None

        try:
            # 使用db_operator验证API Key
            user = verify_api_key(api_key)

            if not user:
                return dbc.Alert("无效的API Key", color="danger"), {"display": "none"}, {}, None, None, {
                    "display": "none"}, None

            # 创建JWT令牌
            token = create_access_token(api_key)

            # 确定是否显示管理员面板
            is_admin = user["permission"] == 9
            admin_panel_style = {"display": "block"} if is_admin else {"display": "none"}

            return (
                dbc.Alert("验证成功", color="success"),
                {"display": "block"},
                {"display": "none"},
                token,
                user["id"],
                admin_panel_style,
                user["permission"]
            )
        except Exception as e:
            logger.error(f"API Key验证失败: {str(e)}")
            return dbc.Alert(f"验证失败: {str(e)}", color="danger"), {"display": "none"}, {}, None, None, {
                "display": "none"}, None
