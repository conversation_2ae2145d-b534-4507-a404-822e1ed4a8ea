# conversation_callbacks.py 负责处理对话相关的回调函数
import dash
from dash import Input, Output, State, ALL, html
from dash.exceptions import PreventUpdate
import dash_bootstrap_components as dbc
import json
import logging
from utils.ui_helpers import refresh_conversation_messages
from db_operator import (
    get_user_conversations_list,
    create_new_conversation,

    get_conversation_messages_list,
    delete_conversation,
    set_current_conversation
)

# 初始化日志
logger = logging.getLogger(__name__)


def register_conversation_callbacks(app):
    @app.callback(
        [Output("conversations-list", "children"),
         Output("store-conversation-list", "data")],
        [Input("store-user-id", "data"),
         Input("conversation-refresh", "n_intervals"),
         Input("store-conversation-selected", "data")],
        prevent_initial_call=True
    )
    def load_conversations_callback(user_id, n_intervals, selected_conversation):
        """加载用户会话列表并展示在侧边栏"""
        if not user_id:
            raise PreventUpdate

        # 获取当前选中的会话ID
        selected_conv_id = selected_conversation.get("id") if selected_conversation else None

        try:
            # 获取用户会话列表
            conversations = get_user_conversations_list(user_id)

            # 如果没有会话，返回提示信息
            if not conversations:
                return html.P("没有对话记录"), {}

            conversation_items = []
            conversation_data = {}

            # 构建会话项列表
            for conv in conversations:
                # 确定该会话是否被选中
                is_selected = conv.get("id") == selected_conv_id

                # 构建会话项元素
                item = create_conversation_item(conv, is_selected)

                # 添加到结果列表和数据存储
                conversation_items.append(item)
                conversation_data[str(conv["id"])] = conv

            return conversation_items, conversation_data

        except Exception as e:
            logger.error(f"加载对话列表失败: {str(e)}")
            return html.P(f"加载失败: {str(e)}"), {}

    def create_conversation_item(conversation, is_selected=False):
        """创建单个会话项UI元素"""
        # 决定会话项类名
        item_class = f"conversation-item{' selected-conversation' if is_selected else ''}"

        # 决定标题类名
        title_class = "conversation-title-text"
        if conversation.get("important"):
            title_class += " text-danger"

        return html.Div([
            # 复选框组件
            html.Div([
                dbc.Checkbox(
                    id={"type": "conversation-checkbox", "index": conversation["id"]},
                    className="small-checkbox"
                )
            ], className="conversation-select-container"),

            # 会话按钮组件
            dbc.Button([
                html.Div([
                        html.Span(conversation["title"], className=title_class)
                ], className="conversation-content")
            ],
                id={"type": "conversation-btn", "index": conversation["id"]},
                color="link",
                className="conversation-button"),
        ],
            id={"type": "conversation-item", "index": conversation["id"]},
            className=item_class)

    @app.callback(
        [Output("store-conversation-checked-list", "data"),
         Output("delete-selected-btn", "disabled")],
        [Input({"type": "conversation-checkbox", "index": ALL}, "value"),
         Input({"type": "conversation-checkbox", "index": ALL}, "id")],
        prevent_initial_call=True
    )
    def update_checked_conversations(checked_values, checkbox_ids):
        if not checked_values or not checkbox_ids:
            return [], True

        checked_conversations = []
        for i, checked in enumerate(checked_values):
            if checked and i < len(checkbox_ids):
                checked_conversations.append(checkbox_ids[i]["index"])

        delete_button_disabled = len(checked_conversations) == 0
        return checked_conversations, delete_button_disabled

    @app.callback(
        [Output("conversations-list", "children", allow_duplicate=True),
         Output("store-conversation-list", "data", allow_duplicate=True),
         Output("store-conversation-selected", "data"),
         Output("current-conversation-title", "children"),
         Output("messages-container", "children", allow_duplicate=True),
         Output("messages-container", "className", allow_duplicate=True),
         Output("store-user-current-conversation-id", "data")],
        [Input("create-conversation-btn", "n_clicks")],
        [State("store-user-id", "data"),
         State("store-user-use-mathjax", "data")],
        prevent_initial_call=True
    )
    def create_conversation_callback(n_clicks, user_id, mathjax):
        if not n_clicks or not user_id:
            raise PreventUpdate

        try:
            # 创建新对话
            conversation = create_new_conversation(user_id)
            if not conversation:
                raise Exception("创建对话失败")

            # 重新加载对话列表
            conversations = get_user_conversations_list(user_id)
            conversation_items = []
            conversation_data = {}

            for conv in conversations:
                # 新创建的对话会被高亮
                item = create_conversation_item(conv, conv["id"] == conversation["id"])
                conversation_items.append(item)
                conversation_data[str(conv["id"])] = conv

            # 使用公共函数获取消息组件（新对话通常没有消息）
            message_components, container_class_name = refresh_conversation_messages(
                conversation["id"], mathjax
            )

            return (
                conversation_items,
                conversation_data,
                conversation,
                conversation["title"],
                message_components,
                container_class_name,
                conversation["id"]
            )
        except Exception as e:
            logger.error(f"创建对话失败: {str(e)}")
            return html.P(f"创建失败: {str(e)}"), {}, None, "选择或创建一个对话", [], "messages-container", None

    @app.callback(
        [Output("current-conversation-title", "children", allow_duplicate=True),
         Output("store-conversation-selected", "data", allow_duplicate=True),
         Output("messages-container", "children", allow_duplicate=True),
         Output("messages-container", "className", allow_duplicate=True),
         Output("conversation-actions", "children"),
         Output("store-user-current-conversation-id", "data", allow_duplicate=True)],
        [Input({"type": "conversation-btn", "index": ALL}, "n_clicks")],
        [State({"type": "conversation-btn", "index": ALL}, "id"),
         State("store-conversation-list", "data"),
         State("store-user-id", "data"),
         State("store-user-use-mathjax", "data")
         ],
        prevent_initial_call=True
    )
    def select_conversation_callback(n_clicks_list, btn_ids, conversation_list, user_id, mathjax):
        if not n_clicks_list or not any(n_clicks_list) or not btn_ids or not conversation_list or not user_id:
            raise PreventUpdate

        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]
        triggered_component = json.loads(triggered_id)
        conversation_id = triggered_component['index']

        conv_id_str = str(conversation_id)

        if conv_id_str not in conversation_list:
            return "对话不存在", None, [], "messages-container", [], None

        conversation = conversation_list[conv_id_str]

        try:
            # 设置用户当前对话
            set_current_conversation(user_id, conversation_id)

            # 使用公共函数获取消息组件和容器类名
            message_components, container_class_name = refresh_conversation_messages(
                conversation_id, mathjax
            )

            # 对话操作按钮
            conversation_actions = html.Div([])

            return (
                conversation["title"],
                conversation,
                message_components,
                container_class_name,
                conversation_actions,
                conversation_id
            )
        except Exception as e:
            logger.error(f"加载对话消息失败: {str(e)}")
            return "加载失败", None, [html.P(f"加载消息失败: {str(e)}")], "messages-container", [], None
    @app.callback(
        [Output("delete-conversations-modal", "is_open"),
         Output("selected-conversations-list", "children")],
        [Input("delete-selected-btn", "n_clicks")],
        [State("store-conversation-checked-list", "data"),
         State("store-conversation-list", "data")],
        prevent_initial_call=True
    )
    def show_delete_confirmation(n_clicks, selected_conversations, conversation_list):
        if not n_clicks or not selected_conversations:
            raise PreventUpdate

        # 显示选中的对话列表
        selected_list = []
        for conv_id in selected_conversations:
            conv_id_str = str(conv_id)
            if conv_id_str in conversation_list:
                selected_list.append(html.Li(conversation_list[conv_id_str]["title"]))

        # 确保模态框能显示，即使没有选择对话
        if not selected_list:
            selected_list = [html.Li("未选择任何对话")]

        return True, html.Ul(selected_list)

    @app.callback(
        Output("delete-conversations-modal", "is_open", allow_duplicate=True),
        [Input("cancel-delete-conversations", "n_clicks")],
        prevent_initial_call=True
    )
    def cancel_delete_conversations(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        return False

    @app.callback(
        [Output("store-conversation-list", "data", allow_duplicate=True),
         Output("conversations-list", "children", allow_duplicate=True),
         Output("delete-conversations-modal", "is_open", allow_duplicate=True),
         Output("store-conversation-checked-list", "data", allow_duplicate=True),
         Output("delete-selected-btn", "disabled", allow_duplicate=True),
         Output("current-conversation-title", "children", allow_duplicate=True),
         Output("messages-container", "children", allow_duplicate=True),
         Output("store-conversation-selected", "data", allow_duplicate=True)],
        [Input("confirm-delete-conversations", "n_clicks")],
        [State("store-conversation-checked-list", "data"),
         State("store-user-id", "data"),
         State("store-conversation-selected", "data")],
        prevent_initial_call=True
    )
    def delete_selected_conversations_callback(n_clicks, selected_conversations, user_id, current_conversation):
        if not n_clicks or not selected_conversations or not user_id:
            raise PreventUpdate

        current_conversation_id = None
        if current_conversation:
            current_conversation_id = current_conversation.get("id")

        try:
            # 删除所有选中的对话
            for conv_id in selected_conversations:
                delete_conversation(conv_id)

            # 重新加载对话列表
            conversations = get_user_conversations_list(user_id)

            conversation_items = []
            conversation_data = {}

            for conv in conversations:
                item = html.Div([
                    dbc.Checkbox(
                        id={"type": "conversation-checkbox", "index": conv["id"]},
                        className="mr-2"
                    ),
                    dbc.Button(
                        conv["title"],
                        id={"type": "conversation-btn", "index": conv["id"]},
                        color="link",
                        className="text-left flex-grow-1"
                    )
                ], className="d-flex align-items-center mb-1")

                conversation_items.append(item)
                conversation_data[str(conv["id"])] = conv

            if not conversation_items:
                conversation_items = [html.P("没有对话记录")]

            # 检查当前选中的对话是否已被删除
            current_conversation_was_deleted = current_conversation_id in selected_conversations

            if current_conversation_was_deleted:
                # 如果当前对话被删除，清空对话内容和标题
                return (
                    conversation_data,
                    conversation_items,
                    False,  # 关闭确认对话框
                    [],  # 清空选中的对话
                    True,  # 禁用删除按钮
                    "选择或创建一个对话",  # 重置对话标题
                    [],  # 清空消息容器
                    None  # 清空当前对话数据
                )
            else:
                # 如果当前对话未被删除，只更新对话列表
                return (
                    conversation_data,
                    conversation_items,
                    False,  # 关闭确认对话框
                    [],  # 清空选中的对话
                    True,  # 禁用删除按钮
                    dash.no_update,  # 保持当前对话标题
                    dash.no_update,  # 保持当前消息
                    dash.no_update  # 保持当前对话数据
                )
        except Exception as e:
            logger.error(f"删除对话失败: {str(e)}")
            raise PreventUpdate