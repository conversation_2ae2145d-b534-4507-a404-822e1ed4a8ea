import json
import os
import dash
from dash import Input, Output, State, html, ALL, dcc
from dash.exceptions import PreventUpdate
import dash_bootstrap_components as dbc
import logging
import base64
import io
from PIL import Image
from utils.image_processor import process_image_to_base64

# 初始化日志
logger = logging.getLogger(__name__)


def register_image_callbacks(app):
    # 处理上传的图片
    @app.callback(
        [Output("message-input", "value", allow_duplicate=True),
         Output("store-image-urls", "data"),
         Output("image-upload-status", "children")],
        [Input("upload-image", "contents"),
         Input("upload-image", "filename")],
        [State("message-input", "value"),
         State("store-image-urls", "data")],
        prevent_initial_call=True
    )
    def process_uploaded_image(contents, filename, current_message, image_urls):
        # 检查触发回调的是否是contents变化
        ctx = dash.callback_context
        if not ctx.triggered or ctx.triggered[0]['prop_id'] == 'upload-image.filename':
            raise PreventUpdate

        if not contents:
            # 清空上传状态
            return current_message, image_urls, html.Div()

        try:
            # 解析base64内容
            content_type, content_string = contents.split(",")
            content_type = content_type.split(";")[0].replace("data:", "")
            decoded = base64.b64decode(content_string)

            # 计算文件大小（KB）
            file_size = len(decoded) / 1024
            size_text = f"{file_size:.1f} KB"
            if file_size > 1024:
                size_text = f"{file_size/1024:.1f} MB"

            # 显示处理状态
            upload_status = html.Div([
                html.Small(f"处理图片: {filename} ({size_text})", className="text-muted"),
                dbc.Progress(value=100, animated=True, style={"height": "5px"})
            ])

            # 检查是否为BMP格式并在前端转换
            if "bmp" in content_type.lower() or (filename and filename.lower().endswith('.bmp')):
                try:
                    img = Image.open(io.BytesIO(decoded))
                    # 创建一个新的BytesIO对象来保存PNG
                    png_io = io.BytesIO()
                    img.save(png_io, format='PNG', optimize=True)
                    png_io.seek(0)
                    # 更新内容为PNG
                    decoded = png_io.getvalue()
                    content_type = "image/png"
                    # 更新文件名
                    if filename:
                        filename = os.path.splitext(filename)[0] + ".png"
                    logger.info(f"已在前端将BMP图片转换为PNG: {filename}")
                except Exception as e:
                    logger.error(f"前端BMP转PNG失败: {e}")

            # 使用图片处理工具
            img_url = process_image_to_base64(decoded, content_type, filename)

            if not img_url:
                logger.error(f"图片处理失败: {filename}")
                return current_message, image_urls, html.Div("图片处理失败", className="text-danger")

            logger.info(f"处理图片成功: {filename}")

            # 不修改消息文本
            new_message = current_message or ""

            # 更新上传的图片列表
            if not image_urls:
                image_urls = []
            image_urls.append(img_url)

            # 处理完成状态
            upload_status = html.Div([
                html.Small(f"图片已处理: {filename} ({size_text})", className="text-success"),
                dbc.Progress(value=100, style={"height": "5px"})
            ])

            return new_message, image_urls, upload_status

        except Exception as e:
            logger.error(f"处理上传图片失败: {e}")
            return current_message, image_urls, html.Div(f"上传失败: {str(e)}", className="text-danger")

    # 清除上传状态（当有新的图片添加到预览区域时）
    @app.callback(
        Output("image-upload-status", "children", allow_duplicate=True),
        [Input("image-preview-area", "children")],
        prevent_initial_call=True
    )
    def clear_upload_status(_):
        return html.Div()

    # 其他回调保持不变
    @app.callback(
        Output("image-preview-area", "children"),
        [Input("store-image-urls", "data")]
    )
    def update_image_preview(image_urls):
        if not image_urls or len(image_urls) == 0:
            return []

        previews = []
        for i, img_url in enumerate(image_urls):
            # 使用索引而不是URL作为按钮ID
            previews.append(html.Div([
                html.Img(src=img_url, style={"maxHeight": "60px", "maxWidth": "60px", "margin": "5px"}),
                html.Button("×",
                            id={"type": "remove-image", "index": str(i)},  # 使用索引作为ID
                            className="btn btn-sm btn-danger position-absolute top-0 right-0",
                            style={"borderRadius": "50%", "width": "20px", "height": "20px", "padding": "0"})
            ], style={"position": "relative", "display": "inline-block"}))

        return html.Div([
            html.Div("将发送以下图片：", className="mb-1 text-muted small"),
            html.Div(previews, style={"display": "flex", "flexWrap": "wrap"})
        ])

    # 删除图片的回调保持不变
    @app.callback(
        Output("store-image-urls", "data", allow_duplicate=True),
        [Input({"type": "remove-image", "index": ALL}, "n_clicks")],
        [State("store-image-urls", "data")],
        prevent_initial_call=True
    )
    def remove_image(n_clicks_list, image_urls):
        if not n_clicks_list or not any(n_clicks_list) or not image_urls:
            raise PreventUpdate

        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        button_id = ctx.triggered[0]['prop_id'].split('.')[0]

        try:
            button_id_dict = json.loads(button_id)
            index_to_remove = int(button_id_dict["index"])

            # 复制一个新列表而不是直接修改原列表
            updated_urls = image_urls.copy()
            if 0 <= index_to_remove < len(updated_urls):
                del updated_urls[index_to_remove]

            return updated_urls
        except Exception as e:
            logger.error(f"删除图片失败: {e}, ID: {button_id}")
            raise PreventUpdate

    # 添加回调更新提示
    @app.callback(
        Output("image-count-badge", "children"),
        [Input("store-image-urls", "data")]
    )
    def update_image_count(image_urls):
        if not image_urls or len(image_urls) == 0:
            return ""

        return html.Span(
            f"{len(image_urls)}张图片",
            className="badge bg-primary"
        )
