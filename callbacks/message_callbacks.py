# message_callbacks.py 负责处理用户消息的相关回调函数
import time
import json
import logging
import dash
from dash import Input, Output, State, ALL, html, ClientsideFunction
from dash.exceptions import PreventUpdate
from openaiSDK import OpenAISDK
from utils.ui_helpers import create_message_component, refresh_conversation_messages

from db_operator import (
    get_conversation_data,
    get_user_model_id,
    get_user_temperature,
    get_model_data,
    create_message,
    update_conversation_title,
    get_message_data,
    update_message_content,
    delete_message,
    get_conversation_messages_list
)

# 初始化日志
logger = logging.getLogger(__name__)


def register_message_callbacks(app):
    # 注册客户端回调函数来接收消息更新事件
    app.clientside_callback(
        ClientsideFunction(
            namespace='clientside',
            function_name='refreshMessagesOnEvent'
        ),
        Output('messages-event-trigger', 'children'),
        [Input('messages-update-event', 'data')]
    )

    @app.callback(
    [Output("message-input", "value"),
     Output("ai-response-status", "children"),
     Output("store-pending-response", "data"),  # 新增：存储等待响应的会话信息
     Output("store-image-urls", "data", allow_duplicate=True)],
    [Input("send-message-btn", "n_clicks")],
    [State("message-input", "value"),
     State("store-conversation-selected", "data"),
     State("store-user-id", "data"),
     State("store-user-current-model-id", "data"),
     State("store-user-current-temperature", "data"),
     State("store-image-urls", "data")],
    prevent_initial_call=True
    )
    def send_user_message(
            n_clicks, message, conversation, user_id,
            model_id, temperature, image_urls):

        # 检查是否有消息内容
        if not message and not (image_urls and len(image_urls) > 0):
            ai_status = html.Div([
                html.I(className="fas fa-exclamation-circle me-1", style={"color": "orange"}),
                "请输入消息内容或上传图片"
            ], className="text-warning")
            return message, ai_status, None, image_urls or []

        # 检查是否选择了对话
        if not conversation:
            ai_status = html.Div([
                html.I(className="fas fa-exclamation-circle me-1", style={"color": "red"}),
                "请先创建或选择一个对话"
            ], className="text-danger")
            return message, ai_status, None, image_urls or []

        # 检查用户ID
        if not user_id:
            raise PreventUpdate

        try:
            # 获取对话对象
            conv_obj = get_conversation_data(conversation["id"])
            if not conv_obj:
                raise Exception("无法获取对话信息")

            # 获取模型信息
            if not model_id:
                model_id = get_user_model_id(user_id)

            # 获取温度设置
            if temperature is None:
                temperature = get_user_temperature(user_id)
            temperature = float(temperature)

            # 获取模型
            model = get_model_data(model_id)
            if not model:
                raise Exception(f"未找到模型ID: {model_id}")

            # 存储在数据库中的内容 - JSON字符串格式，表示多模态内容
            content_items = []

            # 添加文本内容（如果有）
            if message:
                content_items.append({
                    "type": "text",
                    "text": message
                })

            # 添加图片URL（如果有）
            if image_urls and len(image_urls) > 0:
                for img_url in image_urls:
                    url = img_url
                    if isinstance(img_url, dict) and "data_url" in img_url:
                        url = img_url["data_url"]

                    content_items.append({
                        "type": "image_url",
                        "image_url": {
                        "url": url
                        }
                    })

            # 将内容转为JSON字符串，以便存入数据库
            content_json = json.dumps(content_items, ensure_ascii=False)

            # 创建用户消息记录
            user_message = create_message(
                conversation_id=conversation["id"],
                role="user",
                content=content_json,
                model_id=model_id,
                temperature=temperature
            )

            # 更新对话标题（如果是第一条消息）
            existing_messages = get_conversation_messages_list(conversation["id"])
            is_new_conversation = len(existing_messages) <= 1
            new_title = conversation.get("title", "新对话")
            if is_new_conversation:
                if message:
                    if len(message) > 17:
                        title_text = message[:17] + "..."
                    else:
                        title_text = message
                else:
                    title_text = "包含图片的对话"

                update_success = update_conversation_title(conversation["id"], title_text)
                if update_success:
                    new_title = title_text

            # 显示等待状态
            ai_status = html.Div([
                html.I(className="fas fa-spinner fa-spin me-1", style={"color": "blue"}),
                "AI正在思考中..."
            ], className="text-info")

            # 存储待处理信息，用于后续获取AI回复
            pending_data = {
                "conversation_id": conversation["id"],
                "user_id": user_id,
                "message_id": user_message["id"],
                "model_id": model_id,
                "title": new_title,
                "timestamp": time.time()
            }

            # 在新线程中启动AI响应生成
            import threading
            def generate_ai_response():
                try:
                   openai_sdk = OpenAISDK(user_id=user_id)
                   openai_sdk.generate_response_sync()
                except Exception as e:
                    logger.error(f"AI响应生成失败: {str(e)}")

            # 创建并启动线程
            ai_thread = threading.Thread(target=generate_ai_response)
            ai_thread.daemon = True  # 设置为守护线程，主程序结束时线程也会结束
            ai_thread.start()

            # 清空消息输入框和图片
            return "", ai_status, pending_data, []

        except Exception as e:
            logger.error(f"发送消息失败: {str(e)}")
            ai_status = html.Div([
                html.I(className="fas fa-exclamation-circle me-1", style={"color": "red"}),
                f"发送失败: {str(e)}"
            ], className="text-danger")
            return message, ai_status, None, image_urls or []
    # 新增：定期检查AI回复状态
    @app.callback(
        [Output("ai-response-status", "children", allow_duplicate=True),
         Output("current-conversation-title", "children", allow_duplicate=True),
         Output("store-conversation-selected", "data", allow_duplicate=True),
         Output("messages-update-event", "data"),
         Output("store-pending-response", "data", allow_duplicate=True)],
        [Input("interval-component", "n_intervals")],
        [State("store-pending-response", "data")],
        prevent_initial_call=True
    )
    def check_ai_response_status(n_intervals, pending_data):
        if not pending_data:
            raise PreventUpdate

        try:
            # 检查是否已经生成AI回复
            conversation_id = pending_data["conversation_id"]
            user_id = pending_data["user_id"]

            # 这里需要添加一个函数来检查AI是否已回复
            # 例如，可以检查最新消息是否是AI的回复
            messages = get_conversation_messages_list(conversation_id)
            if not messages:
                # 必须返回所有输出的占位符
                return dash.no_update, dash.no_update, dash.no_update, dash.no_update, pending_data

            # 查找是否有AI回复（最新消息是否是assistant角色）
            latest_message = messages[-1]

            if latest_message["role"] == "assistant":
                # AI已回复，更新状态
                ai_status = html.Div([
                    html.I(className="fas fa-check-circle me-1", style={"color": "green"}),
                    "回复已完成"
                ], className="text-success")

                # 触发消息更新
                event_data = {"conversation_id": conversation_id, "timestamp": time.time()}

                # 返回最终结果并清除pending状态
                return ai_status, pending_data["title"], {"id": conversation_id,
                                                          "title": pending_data["title"]}, event_data, None
            else:
                # AI尚未回复，保持等待状态
                # 继续等待，但必须返回所有输出的占位符
                return dash.no_update, dash.no_update, dash.no_update, dash.no_update, pending_data

        except Exception as e:
            logger.error(f"检查AI回复状态失败: {str(e)}")
            # 出错时更新状态
            ai_status = html.Div([
                html.I(className="fas fa-exclamation-circle me-1", style={"color": "red"}),
                f"获取AI回复失败: {str(e)}"
            ], className="text-danger")

            # 必须为所有5个输出提供返回值
            return ai_status, dash.no_update, dash.no_update, dash.no_update, None

    # 添加一个新的回调来处理消息更新事件，刷新消息容器
    @app.callback(
        [Output("messages-container", "children"),
         Output("messages-container", "className")],
        [Input("messages-update-event", "data")],
        [State("store-conversation-selected", "data"),
         State("store-user-use-mathjax", "data")],
        prevent_initial_call=True
    )
    def refresh_messages(event_data, conversation, mathjax):
        if not event_data or not conversation:
            raise PreventUpdate

        try:
            # 获取对话ID
            conversation_id = conversation.get("id")
            if not conversation_id:
                raise PreventUpdate

            # 使用公共函数刷新消息
            return refresh_conversation_messages(conversation_id, mathjax)
        except Exception as e:
            logger.error(f"刷新消息失败: {str(e)}")
            return [], "messages-container"


    # 修改 message_callbacks.py 中的编辑消息回调

    @app.callback(
        [Output("edit-message-modal", "is_open"),
         Output("edit-message-textarea", "value"),
         Output("store-edited-message", "data")],
        [Input({"type": "edit-message-btn", "index": ALL}, "n_clicks")],
        [State("edit-message-modal", "is_open")],
        prevent_initial_call=True
    )
    def open_edit_message_modal(n_clicks_list, is_open):
        if not n_clicks_list or not any(n_clicks_list):
            raise PreventUpdate

        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        button_id = ctx.triggered[0]['prop_id'].split('.')[0]
        message_id = json.loads(button_id)['index']

        try:
            # 获取消息
            message = get_message_data(message_id)
            if not message:
                return is_open, "", None

            # 获取消息内容
            content = message.get("content", "")

            # 如果内容是JSON格式，提取文本部分
            try:
                content_obj = json.loads(content)
                if isinstance(content_obj, list):
                    text_parts = []
                    for item in content_obj:
                        if item.get("type") == "text":
                            text_parts.append(item.get("text", ""))
                    content = "\n".join(text_parts)
            except (json.JSONDecodeError, TypeError):
                # 不是JSON格式，使用原始内容
                pass

            return True, content, {"id": message_id, "role": message["role"]}
        except Exception as e:
            logger.error(f"打开编辑消息模态窗口失败: {str(e)}")
            return is_open, "", None


    # 编辑消息弹窗 - 取消
    @app.callback(
        Output("edit-message-modal", "is_open", allow_duplicate=True),
        [Input("cancel-edit-message", "n_clicks")],
        prevent_initial_call=True
    )
    def cancel_edit_message(n_clicks):
        if not n_clicks:
            raise PreventUpdate
        return False

    # 编辑消息弹窗 - 保存
    @app.callback(
    [Output("edit-message-modal", "is_open", allow_duplicate=True),
     Output("edit-message-status", "children"),
     Output("messages-container", "children", allow_duplicate=True),
     Output("messages-container", "className", allow_duplicate=True)],
    [Input("save-edit-message", "n_clicks")],
    [State("edit-message-textarea", "value"),
     State("store-edited-message", "data"),
     State("store-conversation-selected", "data"),
     State("store-user-use-mathjax", "data")],
    prevent_initial_call=True
    )
    def save_edited_message(n_clicks, new_content, message_data, conversation, mathjax):
        if not n_clicks or not message_data or not conversation:
            raise PreventUpdate

        try:
            message_id = message_data["id"]
            conversation_id = conversation.get("id")

            # 获取原始消息
            original_message = get_message_data(message_id)
            if not original_message:
                return False, html.Div("消息不存在", className="text-danger"), dash.no_update, dash.no_update

            # 如果原始内容是JSON格式，更新文本部分
            try:
                content_obj = json.loads(original_message["content"])
                if isinstance(content_obj, list):
                    # 更新文本部分，保留图片
                    updated_content = []
                    has_text = False

                    for item in content_obj:
                        if item.get("type") == "text":
                            if not has_text:  # 只更新第一个文本部分
                                updated_content.append({"type": "text", "text": new_content})
                                has_text = True
                            # 忽略其他文本部分
                        elif item.get("type") == "image_url":
                            # 保留图片
                            updated_content.append(item)

                    # 如果没有文本部分，添加一个
                    if not has_text:
                        updated_content.insert(0, {"type": "text", "text": new_content})

                    # 转换为JSON字符串
                    new_content = json.dumps(updated_content, ensure_ascii=False)
                else:
                    # 不是列表格式，创建新的JSON格式
                    new_content = json.dumps([{"type": "text", "text": new_content}], ensure_ascii=False)
            except (json.JSONDecodeError, TypeError):
                # 不是JSON格式，创建新的JSON格式
                new_content = json.dumps([{"type": "text", "text": new_content}], ensure_ascii=False)
            # 更新数据库中的消息
            updated_message = update_message_content(message_id, new_content)
            if not updated_message:
                return False, html.Div("消息更新失败", className="text-danger"), dash.no_update, dash.no_update

            # 清除渲染缓存
            clear_rendered_content(message_id)
            # 使用公共函数刷新消息容器
            message_components, container_class_name = refresh_conversation_messages(
                conversation_id, mathjax
            )
            return False, html.Div("消息已更新", className="text-success"), message_components, container_class_name
        except Exception as e:
            logger.error(f"保存编辑消息失败: {str(e)}")
            return False, html.Div(f"更新失败: {str(e)}", className="text-danger"), dash.no_update, dash.no_update

    # 删除消息
    @app.callback(
        [Output("messages-container", "children", allow_duplicate=True),
         Output("messages-container", "className", allow_duplicate=True)],
        [Input({"type": "delete-message-btn", "index": ALL}, "n_clicks")],
        [State("store-conversation-selected", "data"),
         State("store-user-use-mathjax", "data")],
        prevent_initial_call=True
    )
    def delete_single_message_callback(n_clicks_list, conversation, mathjax):
        if not n_clicks_list or not any(n_clicks_list) or not conversation:
            raise PreventUpdate

        ctx = dash.callback_context
        if not ctx.triggered:
            raise PreventUpdate

        button_id = ctx.triggered[0]['prop_id'].split('.')[0]
        message_id = json.loads(button_id)['index']
        conversation_id = conversation.get("id")

        try:
            # 从数据库删除消息
            success = delete_message(message_id)
            if not success:
                raise Exception("无法删除消息")

            # 使用公共函数刷新消息容器
            return refresh_conversation_messages(conversation_id, mathjax)
        except Exception as e:
            logger.error(f"删除消息失败: {str(e)}")
            return [], "messages-container"
