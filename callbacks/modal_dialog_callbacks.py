#modal_callback.py

from dash.dependencies import Input, Output, State
import dash


def register_modal_dailog_callbacks(app):

    @app.callback(
        Output("api-key-display-modal", "is_open", allow_duplicate=True),
        [Input("close-api-key-modal", "n_clicks")],
        [State("api-key-display-modal", "is_open")]
        , prevent_initial_call=True
    )
    def toggle_api_key_modal(close_clicks, is_open):
        # 当"我已保存密钥"按钮被点击时，关闭模态窗口
        if close_clicks:
            return False
        return is_open
    #
    # @app.callback(
    #     Output("edit-message-modal", "is_open"),
    #     [Input("cancel-edit-message", "n_clicks"),
    #      Input("save-edit-message", "n_clicks")],
    #     [State("edit-message-modal", "is_open")],
    #     prevent_initial_call=True
    # )
    # def toggle_edit_message_modal(cancel_clicks, save_clicks, is_open):
    #     """
    #     控制编辑消息模态窗口的打开和关闭
    #
    #     Args:
    #         cancel_clicks: 取消按钮的点击次数
    #         save_clicks: 保存按钮的点击次数
    #         is_open: 当前模态窗口的打开状态
    #
    #     Returns:
    #         bool: 更新后的模态窗口打开状态
    #     """
    #     ctx = dash.callback_context
    #
    #     # 如果没有触发的组件，保持当前状态
    #     if not ctx.triggered:
    #         return is_open
    #
    #     # 获取触发回调的按钮ID
    #     button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    #
    #     # 无论是点击了取消还是保存按钮，都关闭对话框
    #     if button_id in ["cancel-edit-message", "save-edit-message"]:
    #         return False
    #
    #     return is_open

    # @app.callback(
    #     [Output("edit-message-status", "children"),
    #      Output("conversation-container", "children")],  # 或其他需要更新的组件
    #     Input("save-edit-message", "n_clicks"),
    #     [State("edit-message-textarea", "value"),
    #      State("current-editing-message-id", "data")],  # 假设你存储了正在编辑的消息ID
    #     prevent_initial_call=True
    # )
    # def save_edited_message(n_clicks, message_text, message_id):
    #     """
    #     保存编辑后的消息
    #
    #     Args:
    #         n_clicks: 保存按钮的点击次数
    #         message_text: 编辑后的消息文本
    #         message_id: 正在编辑的消息ID
    #
    #     Returns:
    #         tuple: 状态信息和更新后的对话内容
    #     """
    #     if not n_clicks:
    #         return dash.no_update, dash.no_update
    #
    #     # 执行保存消息的逻辑
    #     # ...
    #
    #     # 返回成功状态和更新后的对话内容
    #     return "消息已成功保存", updated_conversation
    return
