# model_callbacks.py 负责模型选择、模型保存、模型参数更新等功能
from dash import Input, Output, State
from dash.exceptions import PreventUpdate
import logging
import requests
import json

# 修改导入，移除直接数据库操作
from db_operator import (
    update_user_model_preference,
    get_user_data,
    get_user_model_id
)

# 初始化日志
logger = logging.getLogger(__name__)

# 添加API服务配置
API_BASE_URL = "http://localhost:5002/api/v1"
API_KEY = "sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo"

def get_available_models_from_api():
    """从API服务获取可用模型列表"""
    try:
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{API_BASE_URL}/models", headers=headers)
        if response.status_code == 200:
            data = response.json()
            return data.get("models", [])
        else:
            logger.error(f"获取模型列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        logger.error(f"获取模型列表异常: {e}")
        return []

def register_model_callbacks(app):
    @app.callback(
        [Output("model-selector", "options"),
         Output("model-selector", "value"),
         Output("store-user-current-model-id", "data")],
        [Input("store-user-id", "data")],
        prevent_initial_call=True
    )
    def load_models_callback(user_id):
        if not user_id:
            raise PreventUpdate

        try:
            # 使用API服务获取模型列表
            models = get_available_models_from_api()
            # 获取用户信息
            user = get_user_data(user_id)

            if not user:
                raise Exception("无法获取用户数据")

            # 默认使用当前模型，如果没有则使用默认模型，如果还没有则使用第一个模型
            model_id = user.get("current_model_id") or user.get("default_model_id")
            if not model_id and models:
                model_id = models[0]["id"]

            options = []
            for model in models:
                display_name = model.get("name", "未知模型")
                
                # 从模型名称判断模型特性
                is_free = model.get("is_default", False)  # 假设默认模型是免费的
                is_high_price = "opus" in display_name.lower() or "gpt-4" in display_name.lower()
                is_visible_model = "vision" in display_name.lower() or "v" in display_name.lower()
                
                appendix = ""
                # 为模型添加不同的标记
                if is_free:
                    appendix += "🆓 "  # 免费模型
                if is_high_price:
                    appendix += "💎 "  # 高价模型
                if is_visible_model:
                    appendix += "📷 "  # 多模态输入模型
                # 如果有前缀，则添加到显示名称前
                if appendix:
                    display_name += appendix

                options.append({
                    "label": display_name,
                    "value": model["id"]
                })

            return options, model_id, model_id
        except Exception as e:
            logger.error(f"加载模型列表失败: {str(e)}")
            return [], None, None

    @app.callback(
        Output("store-user-current-model-id", "data", allow_duplicate=True),
        [Input("model-selector", "value")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def update_user_model_callback(model_id, user_id):
        if not model_id or not user_id:
            raise PreventUpdate

        try:
            # 使用db_operator更新用户模型
            success = update_user_model_preference(user_id, model_id)
            if not success:
                raise PreventUpdate
            return model_id
        except Exception as e:
            logger.error(f"更新用户当前模型失败: {str(e)}")
            raise PreventUpdate

    @app.callback(
        Output("store-user-current-temperature", "data"),
        [Input("temperature-slider", "value")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def update_user_temperature_callback(temperature, user_id):
        if temperature is None or not user_id:
            raise PreventUpdate

        try:
            # 使用db_operator更新用户温度设置
            from db_operator import update_user_temperature_preference
            success = update_user_temperature_preference(user_id, temperature)
            if not success:
                raise PreventUpdate
            return temperature
        except Exception as e:
            logger.error(f"更新用户温度设置失败: {str(e)}")
            raise PreventUpdate
