#settings_callbacks.py文件包含了与设置相关的回调函数
import logging
from dash import Input, Output, State, dash
from dash.exceptions import PreventUpdate
from dash import html
from db_operator import (
    update_user_mathjax_preference,
    get_conversation_messages_list,
    get_user_statistics
)
from utils.ui_helpers import create_message_component

# 初始化日志
logger = logging.getLogger(__name__)

def register_settings_callbacks(app):
    # MathJax设置回调
    @app.callback(
        Output("store-user-use-mathjax", "data"),
        [Input("mathjax-switch", "value")],
        [State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def update_user_mathjax_setting_callback(use_mathjax, user_id):
        if use_mathjax is None or not user_id:
            raise PreventUpdate
        try:
            # 使用db_operator更新用户MathJax设置
            success = update_user_mathjax_preference(user_id, use_mathjax)
            if not success:
                raise PreventUpdate
            return use_mathjax
        except Exception as e:
            logger.error(f"更新MathJax设置失败: {str(e)}")
            raise PreventUpdate

    # 刷新消息以应用新MathJax设置
    @app.callback(
        Output("messages-container", "children", allow_duplicate=True),
        [Input("store-user-use-mathjax", "data")],
        [State("store-conversation-selected", "data"),
         State("store-user-id", "data")],
        prevent_initial_call=True
    )
    def refresh_messages_with_mathjax_callback(mathjax, conversation, user_id):
        if mathjax is None or not conversation or not user_id:
            raise PreventUpdate
        try:
            # 获取当前对话的所有消息
            messages = get_conversation_messages_list(conversation["id"])
            # 使用辅助函数创建消息组件
            message_components = [create_message_component(msg, mathjax) for msg in messages]
            return message_components
        except Exception as e:
            logger.error(f"刷新消息失败: {str(e)}")
            return dash.no_update

    # 用户统计信息回调
    @app.callback(
        Output("user-stats", "children"),
        [Input("store-user-id", "data"),
         Input("user-stats-refresh", "n_intervals"),
         Input("ai-response-status", "children")]
    )
    def load_user_stats_callback(user_id, n_intervals, ai_status):
        if not user_id:
            raise PreventUpdate

        try:
            # 获取用户统计数据
            stats = get_user_statistics(user_id)
            if not stats:
                return html.P("无法加载用户数据")

            # 获取用户数据中的余额信息
            user_data = stats.get("user_data", {})
            current_balance = user_data.get("current_balance", 0.0)
            total_deposited = user_data.get("total_deposited", 0.0)
            total_spent = user_data.get("total_spent", 0.0)

            # 创建格式化的余额显示 - 使用4位小数以提高精度
            balance_display = f"{current_balance:.4f}" if current_balance is not None else "0.0000"
            deposited_display = f"{total_deposited:.4f}" if total_deposited is not None else "0.0000"
            spent_display = f"{total_spent:.4f}" if total_spent is not None else "0.0000"

            return html.Div([
                html.H5("账户信息", className="mb-3"),

                # 余额信息 - 使用更简洁的样式
                html.Div([
                    html.Div([
                        html.Span("当前余额: ", className="text-muted"),
                        html.Span(f"${balance_display}", className="fw-semi-bold ms-2")
                    ], className="d-flex align-items-center mb-2"),

                    html.Div([
                        html.Span("总充值: ", className="text-muted"),
                        html.Span(f"${deposited_display}", className="fw-semi-bold ms-2")
                    ], className="d-flex align-items-center mb-2"),

                    html.Div([
                        html.Span("总消费: ", className="text-muted"),
                        html.Span(f"${spent_display}", className="fw-semi-bold ms-2")
                    ], className="d-flex align-items-center")
                ], className="p-3 border rounded")
            ])
        except Exception as e:
            logger.error(f"加载用户统计失败: {str(e)}")
            return html.P(f"加载统计失败: {str(e)}")