"""
管理员面板组件模块
提供用户管理、资金管理和系统统计功能
"""

from dash import html, dcc
import dash_bootstrap_components as dbc
from styles.theme import COLORS


def create_admin_panel() -> html.Div:
    """
    创建管理员控制面板

    Returns:
        html.Div: 管理员面板容器，包含用户、资金和统计管理功能
    """
    return html.Div([
        dbc.Container([
            html.H2("管理员控制面板", className="mb-4 mt-4"),
            # 管理选项卡
            dbc.Tabs([
                # 用户管理选项卡
                dbc.Tab([
                    html.Div([
                        html.H4("用户列表", className="mt-3 mb-3"),
                        html.Div([
                            dbc.But<PERSON>(
                                "刷新用户列表",
                                id="refresh-users-btn",
                                color="primary",
                                className="me-2 mb-3"
                            ),
                            dbc.<PERSON><PERSON>(
                                "添加新用户",
                                id="add-user-btn",
                                color="success",
                                className="mb-3"
                            ),
                        ]),
                        html.Div(id="users-table-container", className="mt-3"),
                    ])
                ], label="用户管理", tab_id="user-management"),

                # 资金管理选项卡
                dbc.Tab([
                    html.Div([
                        html.H4("用户充值", className="mt-3 mb-3"),
                        dbc.Row([
                            dbc.Col([
                                dbc.Label("选择用户:", className="fw-bold"),
                                dcc.Dropdown(id="deposit-user-selector", className="mb-3"),
                            ], width=6),
                        ]),
                        dbc.Row([
                            dbc.Col([
                                dbc.Label("充值金额($):", className="fw-bold"),
                                dbc.Input(
                                    id="deposit-amount-input",
                                    type="number",
                                    min=0,
                                    step=1,
                                    placeholder="输入充值金额",
                                    className="mb-3"
                                ),
                            ], width=6),
                        ]),
                        dbc.Row([
                            dbc.Col([
                                dbc.Label("备注(可选):", className="fw-bold"),
                                dbc.Input(
                                    id="deposit-description-input",
                                    placeholder="充值描述",
                                    className="mb-3"
                                ),
                            ], width=6),
                        ]),
                        dbc.Row([
                            dbc.Col([
                                dbc.Button(
                                    "确认充值",
                                    id="confirm-deposit-btn",
                                    color="success",
                                    className="mb-3"
                                ),
                            ], width=6),
                        ]),
                        html.Div(id="deposit-status", className="mt-3"),
                    ])
                ], label="资金管理", tab_id="financial-management"),

                # 统计分析选项卡
                dbc.Tab([
                    html.Div([
                        html.H4("系统统计", className="mt-3 mb-3"),
                        dbc.Row([
                            dbc.Col([
                                dbc.Card([
                                    dbc.CardBody([
                                        html.H5("总用户数", className="card-title"),
                                        html.P(
                                            id="total-users-count",
                                            className="display-4 text-center"
                                        )
                                    ])
                                ], className="shadow-sm mb-3")
                            ], width=4),
                            dbc.Col([
                                dbc.Card([
                                    dbc.CardBody([
                                        html.H5("总活跃用户", className="card-title"),
                                        html.P(
                                            id="active-users-count",
                                            className="display-4 text-center"
                                        )
                                    ])
                                ], className="shadow-sm mb-3")
                            ], width=4),
                            dbc.Col([
                                dbc.Card([
                                    dbc.CardBody([
                                        html.H5("总交易金额", className="card-title"),
                                        html.P(
                                            id="total-transaction-amount",
                                            className="display-4 text-center"
                                        )
                                    ])
                                ], className="shadow-sm mb-3")
                            ], width=4),
                        ])
                    ])
                ], label="统计分析", tab_id="statistics"),
            ], id="admin-tabs", active_tab="user-management"),
        ])
    ], id="admin-panel", style={"display": "none"})
