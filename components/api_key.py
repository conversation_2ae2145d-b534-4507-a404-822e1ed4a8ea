"""
API密钥验证组件模块
提供用户输入并验证API密钥的界面
"""

from dash import html
import dash_bootstrap_components as dbc
from styles.theme import COLORS


def create_api_key_area() -> html.Div:
    """
    创建API密钥验证区域

    Returns:
        html.Div: API密钥输入和验证的容器
    """
    return html.Div([
        dbc.Card([
            dbc.CardHeader("欢迎使用Inspirflow", className="text-center h4"),
            dbc.CardBody([
                html.P("涓涓灵感，涌动创意 ---- 一个基于AI的智能对话助手", className="lead text-center mb-4"),
                dbc.Row([
                    dbc.Col([
                        dbc.Label("请输入您的API Key:", className="fw-bold"),
                        dbc.InputGroup([
                            dbc.Input(
                                id="api-key-input",
                                type="password",
                                placeholder="sk-...",
                                className="border-primary"
                            ),
                            dbc.<PERSON>("验证", id="verify-api-key", color="primary"),
                        ]),
                        html.Div(id="api-key-status", className="mt-3"),
                    ], lg=6, md=8, sm=10, xs=12, className="mx-auto")
                ])
            ])
        ], className="shadow")
    ], id="api-key-container", className="container")
