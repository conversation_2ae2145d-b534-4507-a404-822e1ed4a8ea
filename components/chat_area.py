"""
聊天区域组件模块
提供消息显示、输入和图片上传功能
"""

from dash import html, dcc
import dash_bootstrap_components as dbc
from styles.theme import COLORS


def create_chat_area() -> dbc.Card:
    """
    创建聊天区域组件

    Returns:
        dbc.Card: 包含消息显示、输入和控制的卡片组件
    """
    return dbc.Card([
        dbc.CardBody([
            # 对话标题和操作
            dbc.Row([
                dbc.Col(html.H3(
                    id="current-conversation-title",
                    children="选择或创建一个对话",
                    className="card-title"
                )),
                dbc.Col(html.Div(id="conversation-actions"), width="auto"),
            ], className="mb-3 align-items-center"),

            # 功能开关
            dbc.Row([
                dbc.Col([
                    dbc.Switch(
                        id="mathjax-switch",
                        label="启用数学公式渲染",
                        value=True,
                        className="custom-switch"
                    )
                ])
            ], className="mb-3"),

            # 消息显示区域
            html.Div(
                id="messages-container",
                className="border rounded p-3 bg-light",
                style={
                    "height": "500px",
                    "overflowY": "auto",
                    "display": "flex",
                    "flexDirection": "column"
                }
            ),

            # AI响应状态指示器
            html.Div(
                id="ai-response-status",
                className="text-center text-muted my-2 fst-italic"
            ),

            # 输入和控制区域
            dbc.Row([
                # 消息输入和图片预览
                dbc.Col([
                    # 图片预览区域
                    html.Div(id="image-preview-area", className="mb-2"),

                    # 消息输入框
                    dbc.Textarea(
                        id="message-input",
                        placeholder="输入您的消息...",
                        className="border-primary",
                        style={"height": "120px"}
                    ),
                ], width=10, className="message-input-col"),

                # 上传和发送按钮
                dbc.Col([
                    html.Div([
                        # 图片上传
                        dcc.Upload(
                            id="upload-image",
                            children=html.Div([
                                html.I(className="fas fa-image me-2"),
                                "上传图片"
                            ]),
                            style={
                                'width': '100%',
                                'height': '38px',
                                'lineHeight': '38px',
                                'borderWidth': '1px',
                                'borderStyle': 'dashed',
                                'borderRadius': '5px',
                                'textAlign': 'center',
                                'margin': '10px 0px',
                                'cursor': 'pointer',
                                'backgroundColor': '#f8f9fa'
                            },
                            multiple=False
                        ),
                        # 上传状态显示区域
                        html.Div(id="image-upload-status", className="mt-1 mb-2"),

                        # 发送按钮
                        dbc.Button([
                            html.I(className="fas fa-paper-plane me-1"),
                            "发送"
                        ],
                            id="send-message-btn",
                            color="primary",
                            className="w-100 mt-2"
                        ),
                        html.Span(id="image-count-badge", className="ms-2")
                    ], className="d-flex flex-column justify-content-between")
                ], width=2, className="message-action-col"),
            ], className="mt-3"),
        ])
    ], className="shadow-sm")
