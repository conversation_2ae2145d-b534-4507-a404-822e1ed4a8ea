"""
对话管理相关的模态窗口组件
"""

from dash import html
import dash_bootstrap_components as dbc


def create_delete_conversation_modal() -> dbc.Modal:
    """
    创建删除对话确认的模态窗口

    Returns:
        dbc.Modal: 删除对话确认的模态窗口组件
    """
    return dbc.<PERSON>([
        dbc.<PERSON>eader(html.H5("确认删除对话", className="text-danger")),
        dbc.ModalBody([
            html.P([
                html.I(className="fas fa-exclamation-triangle me-2 text-warning"),
                "您确定要删除选中的对话吗？此操作不可撤销。"
            ], className="fw-bold"),
            html.Div([
                html.H6("选中的对话:"),
                html.Div(id="selected-conversations-list", className="border p-2 rounded")
            ])
        ]),
        dbc.<PERSON>Footer([
            dbc.<PERSON><PERSON>([
                html.I(className="fas fa-times me-1"),
                "取消"
            ], id="cancel-delete-conversations", color="secondary"),
            dbc.<PERSON><PERSON>([
                html.I(className="fas fa-trash me-1"),
                "删除"
            ], id="confirm-delete-conversations", color="danger")
        ])
    ], id="delete-conversations-modal", centered=True)
