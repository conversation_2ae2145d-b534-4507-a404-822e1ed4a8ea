"""
消息编辑相关的模态窗口组件
"""

from dash import html
import dash_bootstrap_components as dbc


def create_edit_message_modal() -> dbc.Modal:
    """
    创建编辑消息的模态窗口

    Returns:
        dbc.Modal: 编辑消息的模态窗口组件
    """
    return dbc.<PERSON>([
        dbc.<PERSON>eader("编辑消息"),
        dbc.ModalBody([
            dbc.Textarea(id="edit-message-textarea", style={"height": "200px"}),
            html.Div(id="edit-message-status")
        ]),
        dbc.ModalFooter([
            dbc.<PERSON><PERSON>("取消", id="cancel-edit-message", className="ml-auto", color="secondary"),
            dbc.<PERSON><PERSON>("保存", id="save-edit-message", color="primary")
        ])
    ], id="edit-message-modal")
