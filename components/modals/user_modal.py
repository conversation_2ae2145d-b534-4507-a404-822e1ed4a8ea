"""
用户相关的模态窗口组件
包括添加用户和显示API密钥的模态窗口
"""

from dash import html, dcc
import dash_bootstrap_components as dbc


def create_add_user_modal() -> dbc.Modal:
    """
    创建添加用户的模态窗口

    Returns:
        dbc.Modal: 添加新用户的模态窗口组件
    """
    return dbc.Modal([
        dbc.ModalHeader("添加新用户"),
        dbc.ModalBody([
            dbc.Label("权限等级:", className="fw-bold"),
            dcc.Dropdown(
                id="new-user-permission",
                options=[
                    {"label": "普通用户", "value": 1},
                    {"label": "高级用户", "value": 5},
                    {"label": "管理员", "value": 9}
                ],
                value=1,
                className="mb-3"
            ),
            dbc.Label("初始余额($):", className="fw-bold"),
            dbc.Input(
                id="new-user-balance",
                type="number",
                min=0,
                step=1,
                value=10,
                className="mb-3"
            ),
            html.Div(id="add-user-status")
        ]),
        dbc.ModalFooter([
            dbc.<PERSON><PERSON>("取消", id="cancel-add-user", color="secondary", className="me-2"),
            dbc.Button("创建", id="create-user", color="success")
        ])
    ], id="add-user-modal")


def create_api_key_display_modal() -> dbc.Modal:
    """
    创建显示API密钥的模态窗口

    Returns:
        dbc.Modal: 显示新创建用户API密钥的模态窗口组件
    """
    return dbc.Modal([
        dbc.ModalHeader("新用户已创建"),
        dbc.ModalBody([
            html.P("新用户已成功创建。请保存以下API密钥，它只会显示一次：", className="mb-3"),
            dbc.Alert(id="new-user-api-key", color="warning", className="mb-3"),
            dbc.Alert([
                html.I(className="fas fa-exclamation-triangle me-2"),
                "请立即复制并安全保存此密钥，关闭此窗口后将无法再次查看完整密钥。"
            ], color="danger")
        ]),
        dbc.ModalFooter([
            dbc.Button("我已保存密钥", id="close-api-key-modal", color="primary")
        ])
    ], id="api-key-display-modal")
