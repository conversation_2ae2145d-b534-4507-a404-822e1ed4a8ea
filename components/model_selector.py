"""
模型选择器组件模块
提供AI模型选择和参数调整功能
"""

from dash import html, dcc
import dash_bootstrap_components as dbc
from styles.theme import COLORS

def create_model_selection_panel() -> dbc.Card:
    """
    创建模型选择和参数设置面板

    Returns:
        dbc.Card: 模型选择和温度设置的卡片组件
    """
    return dbc.Card([
        dbc.CardBody([
            html.H5("模型选择", className="card-title mb-3"),
            dcc.Dropdown(id="model-selector", className="mb-3"),
            html.Div([
                dbc.Label("温度值:", className="mb-2"),
                html.Div([
                    dcc.Slider(
                        min=0, max=2, step=0.1, value=0.7,
                        id="temperature-slider",
                        marks=None,
                        className="mb-1"
                    ),
                    html.Div([
                        html.Span("低创造性", className="text-muted float-start"),
                        html.Span("高创造性", className="text-muted float-end")
                    ], className="d-flex justify-content-between w-100")
                ], className="w-100")
            ]),
            html.Hr(),
            # 用户统计信息区域
            html.Div(id="user-stats")
        ])
    ], className="shadow-sm")
