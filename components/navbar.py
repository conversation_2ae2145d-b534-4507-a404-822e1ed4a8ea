"""
导航栏组件模块
提供应用顶部的导航栏，包括品牌Logo、标题和导航链接
"""

from dash import html
import dash_bootstrap_components as dbc
from styles.theme import COLORS


def create_navbar() -> dbc.Navbar:
    """
    创建应用导航栏组件

    Returns:
        dbc.Navbar: 带有品牌、链接的导航栏组件
    """
    return dbc.Navbar([
        dbc.Container([
            html.A(
                dbc.Row([
                    dbc.Col(html.Img(src="/assets/logo.jpg", height="60px"), width="auto"),
                    dbc.Col(dbc.NavbarBrand("Inspirflow", className="ms-2 fw-bold")),
                ], align="center", className="g-0"),
                href="/",
                style={"textDecoration": "none"},
            ),
            dbc.NavbarToggler(id="navbar-toggler"),
            dbc.Collapse(
                dbc.Nav([
                    dbc.NavItem(dbc.NavLink("关于", href="#")),
                    dbc.NavItem(dbc.NavLink("帮助", href="#")),
                ], className="ms-auto"),
                id="navbar-collapse",
                navbar=True,
            ),
        ]),
    ], color='primary', dark=True, className="mb-4")
