"""
侧边栏组件模块
提供对话列表和管理功能的侧边栏
"""

from dash import html, dcc
import dash_bootstrap_components as dbc
from styles.theme import COLORS


def create_sidebar() -> dbc.Card:
    """
    创建对话列表侧边栏

    Returns:
        dbc.Card: 包含对话列表和对话管理功能的卡片组件
    """
    return dbc.Card([
        dbc.CardBody([
            html.H4("对话列表", className="card-title"),
            html.Div([
                dbc.<PERSON><PERSON>(
                    "新建对话",
                    id="create-conversation-btn",
                    color="success",
                    className="me-2"
                ),
                dbc.<PERSON><PERSON>(
                    "删除所选",
                    id="delete-selected-btn",
                    color="danger",
                    disabled=True
                )
            ], className="d-flex justify-content-between mb-3"),
            html.Div(
                id="conversations-list",
                style={"height": "350px", "overflowY": "auto"}
            ),
        ])
    ], className="mb-3 shadow-sm")
