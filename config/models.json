{"deepseek-V3:free": {"platform": "OpenRouter", "model": "deepseek/deepseek-chat:free", "url": "https://openrouter.ai/api/v1", "visible-model": false, "input-price": 0.0, "output-price": 0.0, "picture-price": 0.0, "free": true, "high-price": false}, "deepseek-V3:aliyun": {"platform": "dashscope", "model": "deepseek-v3", "url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "visible-model": false, "input-price": 0.27, "output-price": 1.1, "picture-price": 0.0, "free": false, "high-price": false}, "deepseek-V3:deepseek": {"platform": "deepseek", "model": "deepseek-chat", "url": "https://api.deepseek.com", "visible-model": false, "input-price": 0.27, "output-price": 1.1, "picture-price": 0.0, "free": false, "high-price": false}, "deepseek-r1:free": {"platform": "OpenRouter", "model": "deepseek/deepseek-r1:free", "url": "https://openrouter.ai/api/v1", "visible-model": false, "input-price": 0.0, "output-price": 0.0, "picture-price": 0.0, "free": true, "high-price": false}, "deepseek-r1:aliyun": {"platform": "dashscope", "model": "deepseek-r1", "url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "visible-model": false, "input-price": 0.55, "output-price": 2.2, "picture-price": 0.0, "free": false, "high-price": false}, "deepseek-r1:deepseek": {"platform": "deepseek", "model": "deepseek-reasoner", "url": "https://api.deepseek.com", "visible-model": false, "input-price": 0.55, "output-price": 2.2, "picture-price": 0.0, "free": false, "high-price": false}, "GLM-4-Plus:zhipu": {"platform": "GLM", "model": "glm-4-plus", "url": "https://open.bigmodel.cn/api/paas/v4/", "visible-model": false, "input-price": 7.0, "output-price": 7.0, "picture-price": 0.0, "free": false, "high-price": true}, "GLM-4V-Plus-0111:zhipu": {"platform": "GLM", "model": "GLM-4V-Plus-0111", "url": "https://open.bigmodel.cn/api/paas/v4/", "visible-model": true, "input-price": 7.0, "output-price": 7.0, "picture-price": 0.0, "free": false, "high-price": true}, "O3-mini": {"platform": "deepbricks", "model": "o3-mini", "url": "https://api.deepbricks.ai/v1/", "visible-model": false, "input-price": 1.2, "output-price": 5.0, "picture-price": 0.0, "free": false, "high-price": true}, "GPT-4o-2024-08-06": {"platform": "deepbricks", "model": "gpt-4o-2024-08-06", "url": "https://api.deepbricks.ai/v1/", "visible-model": true, "input-price": 1.0, "output-price": 4.0, "picture-price": 0.0, "free": false, "high-price": false}, "GPT-4o-mini": {"platform": "deepbricks", "model": "gpt-4o-mini", "url": "https://api.deepbricks.ai/v1/", "visible-model": false, "input-price": 0.125, "output-price": 0.5, "picture-price": 0.0, "free": false, "high-price": false}, "claude-3.7-sonnet": {"platform": "OpenRouter", "model": "anthropic/claude-3.7-sonnet", "url": "https://openrouter.ai/api/v1", "visible-model": true, "input-price": 3.0, "output-price": 15.0, "picture-price": 4.8, "free": false, "high-price": true}, "claude-3.5-sonnet": {"platform": "deepbricks", "model": "claude-3.5-sonnet", "url": "https://api.deepbricks.ai/v1/", "visible-model": true, "input-price": 2.0, "output-price": 10.0, "picture-price": 4.8, "free": false, "high-price": true}, "claude-3.5-Haiku": {"platform": "OpenRouter", "model": "anthropic/claude-3.5-haiku", "url": "https://openrouter.ai/api/v1", "visible-model": false, "input-price": 0.8, "output-price": 4.0, "picture-price": 0.0, "free": false, "high-price": false}, "Gemini Flash 2.0": {"platform": "OpenRouter", "model": "google/gemini-2.0-flash-001", "url": "https://openrouter.ai/api/v1", "visible-model": true, "input-price": 0.1, "output-price": 0.4, "picture-price": 0.0258, "free": false, "high-price": false}, "Gemini Pro 2.0 Experimental": {"platform": "OpenRouter", "model": "google/gemini-2.0-pro-exp-02-05:free", "url": "https://openrouter.ai/api/v1", "visible-model": true, "input-price": 0.0, "output-price": 0.0, "picture-price": 0.0, "free": true, "high-price": false}, "Gemini Flash 2.0 thinking Experiment": {"platform": "OpenRouter", "model": "google/gemini-2.0-flash-thinking-exp:free", "url": "https://openrouter.ai/api/v1", "visible-model": false, "input-price": 0.0, "output-price": 0.0, "picture-price": 0.0, "free": true, "high-price": false}}