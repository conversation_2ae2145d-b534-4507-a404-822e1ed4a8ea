# db_models.py (修改版)
import os
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, ForeignKey, Numeric, Boolean, UniqueConstraint, LargeBinary
from sqlalchemy.orm import declarative_base, relationship, sessionmaker, scoped_session
from datetime import datetime
import logging
from sqlalchemy import text
from sqlalchemy import MetaData, Table
# 日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建基类
Base = declarative_base()

# MariaDB连接配置
MARIADB_USER = os.environ.get('MARIADB_USER', 'root')
MARIADB_PASSWORD = os.environ.get('MARIADB_PASSWORD', 'rw80827')
MARIADB_HOST = os.environ.get('MARIADB_HOST', '*************')
MARIADB_PORT = os.environ.get('MARIADB_PORT', '3306')
MARIADB_MODEL_DB = os.environ.get('MARIADB_MODEL_DB', 'model_registry')
MARIADB_CHAT_DB = os.environ.get('MARIADB_CHAT_DB', 'chat_system')

# 创建MariaDB引擎 - 模型注册库
model_db_uri = f'mysql+pymysql://{MARIADB_USER}:{MARIADB_PASSWORD}@{MARIADB_HOST}:{MARIADB_PORT}/{MARIADB_MODEL_DB}'
model_db_engine = create_engine(
    model_db_uri,
    pool_size=10,
    max_overflow=15,
    pool_timeout=60,
    pool_pre_ping=True
)

# 创建MariaDB引擎 - 聊天系统库
chat_db_uri = f'mysql+pymysql://{MARIADB_USER}:{MARIADB_PASSWORD}@{MARIADB_HOST}:{MARIADB_PORT}/{MARIADB_CHAT_DB}'
chat_db_engine = create_engine(
    chat_db_uri,
    pool_size=10,
    max_overflow=15,
    pool_timeout=60,
    pool_pre_ping=True
)

# 创建会话工厂
ModelDBSession = sessionmaker(bind=model_db_engine)
ChatDBSession = sessionmaker(bind=chat_db_engine)

# 创建会话作用域
model_db_session = scoped_session(ModelDBSession)
chat_db_session = scoped_session(ChatDBSession)

# 数据库会话管理
def get_model_db():
    """模型数据库会话上下文管理器"""
    try:
        yield model_db_session
    finally:
        model_db_session.remove()

def get_chat_db():
    """聊天数据库会话上下文管理器"""
    try:
        yield chat_db_session
    finally:
        chat_db_session.remove()

# 为了兼容现有代码，保留原来的get_db函数
def get_db():
    """默认数据库会话上下文管理器（聊天数据库）"""
    try:
        yield chat_db_session
    finally:
        chat_db_session.remove()

# ========== 模型数据库模型定义 ==========
class Platform(Base):
    __tablename__ = 'platforms'
    __table_args__ = {'extend_existing': True, 'schema': MARIADB_MODEL_DB}

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    base_url = Column(String(255), nullable=False)
    api_key = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    models = relationship("AIModel", back_populates="platform")

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'base_url': self.base_url,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class AIModel(Base):
    __tablename__ = 'ai_models'
    __table_args__ = {'extend_existing': True, 'schema': MARIADB_MODEL_DB}

    id = Column(Integer, primary_key=True)
    display_name = Column(String(50), unique=True, nullable=False)
    internal_name = Column(String(100), nullable=False)
    api_endpoint = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

    # 价格字段 (每1000个token的价格，单位为美元)
    input_token_price = Column(Numeric(10, 6), default=0.000000)  # 输入token价格
    output_token_price = Column(Numeric(10, 6), default=0.000000)  # 输出token价格
    input_picture_price = Column(Numeric(10, 6), default=0.000000)  # 输入图片价格
    is_visible_model = Column(Boolean, default=False)
    free = Column(Boolean, default=False)  # 是否免费
    high_price = Column(Boolean, default=False)  # 是否高价

    platform_id = Column(Integer, ForeignKey(f'{MARIADB_MODEL_DB}.platforms.id'), nullable=False)
    platform = relationship("Platform", back_populates="models")

    def to_dict(self):
        return {
            'id': self.id,
            'display_name': self.display_name,
            'platform': self.platform.name,
            'internal_name': self.internal_name,
            'is_visible_model': self.is_visible_model,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'input_token_price': float(self.input_token_price),
            'output_token_price': float(self.output_token_price),
            'input_picture_price': float(self.input_picture_price),
            'free': self.free,
            'high_price': self.high_price
        }

class Application(Base):
    __tablename__ = 'applications'
    __table_args__ = {'extend_existing': True, 'schema': MARIADB_MODEL_DB}

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    api_key = Column(String(255), unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 应用与模型的关系
    app_models = relationship("AppModel", back_populates="application", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'api_key': self.api_key,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class AppModel(Base):
    __tablename__ = 'app_models'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_MODEL_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True)
    application_id = Column(Integer, ForeignKey(f'{MARIADB_MODEL_DB}.applications.id'), nullable=False)
    model_id = Column(Integer, ForeignKey(f'{MARIADB_MODEL_DB}.ai_models.id'), nullable=False)
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    application = relationship("Application", back_populates="app_models")
    model = relationship("AIModel")

    def to_dict(self):
        return {
            'id': self.id,
            'application_id': self.application_id,
            'application_name': self.application.name,
            'model_id': self.model_id,
            'model_name': self.model.display_name,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# ========== 聊天数据库模型定义 ==========
class User(Base):
    __tablename__ = 'users'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_CHAT_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True)
    api_key = Column(String(255), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    permission = Column(Integer, default=1, nullable=False)
    # 上面是基础信息
    mathjax = Column(Boolean, default=False)
    current_model_id = Column(Integer, ForeignKey(f'{MARIADB_MODEL_DB}.ai_models.id'))
    current_temperature = Column(Numeric(3, 2), default=0.7)
    current_conversation_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.conversations.id'))
    # 新增余额相关字段
    total_deposited = Column(Numeric(10, 4), default=0.0000)  # 总充值金额
    total_spent = Column(Numeric(10, 4), default=0.0000)  # 总消费金额
    current_balance = Column(Numeric(10, 4), default=0.0000)  # 当前余额

    # 新增token使用统计
    total_prompt_tokens = Column(Integer, default=0)  # 总输入token数
    total_completion_tokens = Column(Integer, default=0)  # 总输出token数

    # 关系定义
    conversations = relationship("Conversation",
                                 back_populates="user",
                                 foreign_keys="[Conversation.user_id]")
    current_conversation = relationship("Conversation",
                                        foreign_keys=[current_conversation_id])

    def to_dict(self):
        return {
            #  基本信息
            "id": self.id,
            "api_key": self.api_key,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "is_active": self.is_active,
            "permission": self.permission,
            # 设置信息
            "mathjax": self.mathjax,
            "current_model_id": self.current_model_id,
            "current_temperature": float(self.current_temperature) if self.current_temperature else 0.7,
            "current_conversation_id": self.current_conversation_id,
            # 费用相关字段
            "total_deposited": float(self.total_deposited) if self.total_deposited else 0.0,
            "total_spent": float(self.total_spent) if self.total_spent else 0.0,
            "current_balance": float(self.current_balance) if self.current_balance else 0.0,
            "total_prompt_tokens": self.total_prompt_tokens,
            "total_completion_tokens": self.total_completion_tokens
        }

class Conversation(Base):
    __tablename__ = 'conversations'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_CHAT_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    # 基本信息
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    latest_revised_at = Column(DateTime, default=datetime.utcnow)

    # 纪录
    user_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.users.id'), nullable=False)
    title = Column(String(255))

    # 关系定义
    user = relationship("User",
                        back_populates="conversations",
                        foreign_keys=[user_id])
    messages = relationship("Message",
                            back_populates="conversation",
                            cascade="all, delete-orphan")

    def to_dict(self):
        return {
            # 基本信息
            'id': self.id,
            'created_at': self.created_at.isoformat(),
            'latest_revised_at': self.latest_revised_at.isoformat(),
            # 记录
            'title': self.title,
            'user_id': self.user_id,
            'message_count': len(self.messages)
        }

    def update_title_from_message(self, user_message_content):
        if user_message_content:
            max_length = 30
            self.title = (user_message_content[:max_length] + '...'
                          if len(user_message_content) > max_length
                          else user_message_content)

# 修改 Message 表，移除 content 字段
class Message(Base):
    __tablename__ = 'messages'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_CHAT_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True)
    conversation_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.conversations.id'), nullable=False)
    role = Column(String(20))
    # content 字段已移除，将存储在 MessageContent 表中
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 记录模型ID和温度
    model_id = Column(Integer, ForeignKey(f'{MARIADB_MODEL_DB}.ai_models.id'), nullable=False)
    temperature = Column(Numeric(3, 2))
    max_tokens = Column(Integer)

    # 记录token使用情况
    prompt_tokens = Column(Integer)  # 输入token数量
    completion_tokens = Column(Integer)  # 输出token数量

    # 新增费用字段
    prompt_cost = Column(Numeric(10, 6), default=0.000000)  # 输入token费用
    completion_cost = Column(Numeric(10, 6), default=0.000000)  # 输出token费用
    total_cost = Column(Numeric(10, 6), default=0.000000)  # 总费用

    is_error = Column(Boolean, default=False)
    error_info = Column(Text)

    conversation = relationship("Conversation", back_populates="messages")

    # 关系定义
    content_relation = relationship("MessageContent", back_populates="message", uselist=False, cascade="all, delete-orphan")
    rendered_content = relationship("RenderedContent", back_populates="message", uselist=False, cascade="all, delete-orphan")

    def to_dict(self):
        result = {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'role': self.role,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            # model相关信息
            'model_id': self.model_id,
            'temperature': float(self.temperature) if self.temperature else None,
            'max_tokens': self.max_tokens,
            # 费用相关信息
            'prompt_tokens': self.prompt_tokens,
            'completion_tokens': self.completion_tokens,
            'prompt_cost': float(self.prompt_cost) if self.prompt_cost else 0,
            'completion_cost': float(self.completion_cost) if self.completion_cost else 0,
            'total_cost': float(self.total_cost) if self.total_cost else 0,
            'is_error': self.is_error,
            'error_info': self.error_info
        }

        # 添加内容字段（如果有）
        if self.content_relation:
            result['content'] = self.content_relation.content
        else:
            result['content'] = ""

        return result

# 新增 MessageContent 表，存储消息内容
class MessageContent(Base):
    __tablename__ = 'message_contents'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_CHAT_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True)
    message_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.messages.id'), nullable=False, unique=True)
    content = Column(Text(length=16777215))  # 使用MEDIUMTEXT类型存储更大的内容
    content_hash = Column(String(64))  # 内容的哈希值，用于检测变化
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    message = relationship("Message", back_populates="content_relation")

# 新增 RenderedContent 表，存储渲染后的内容
class RenderedContent(Base):
    __tablename__ = 'rendered_contents'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_CHAT_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True)
    message_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.messages.id'), nullable=False, unique=True)
    rendered_with_mathjax = Column(LargeBinary(length=16777215))  # 使用MEDIUMBLOB存储渲染后的HTML
    rendered_without_mathjax = Column(LargeBinary(length=16777215))  # 不带MathJax的渲染HTML
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    message = relationship("Message", back_populates="rendered_content")
class Transaction(Base):
    __tablename__ = 'transactions'
    __table_args__ = {
        'extend_existing': True,
        'schema': MARIADB_CHAT_DB,
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.users.id'), nullable=False)
    transaction_type = Column(String(20), nullable=False)  # 'deposit' 或 'consumption'
    amount = Column(Numeric(10, 4), nullable=False)
    balance_after = Column(Numeric(10, 4), nullable=False)  # 交易后余额
    description = Column(String(255))
    message_id = Column(Integer, ForeignKey(f'{MARIADB_CHAT_DB}.messages.id'))  # 如果是消费，关联到具体消息
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    user = relationship("User")
    message = relationship("Message")

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'transaction_type': self.transaction_type,
            'amount': float(self.amount),
            'balance_after': float(self.balance_after),
            'description': self.description,
            'message_id': self.message_id,
            'created_at': self.created_at.isoformat()
        }



# 在 db_models.py 中添加数据迁移函数

def migrate_message_content():
    """将现有的 Message 表中的 content 字段迁移到 MessageContent 表中"""
    import hashlib

    session = ChatDBSession()
    try:
        # 检查是否有 content 字段
        inspector = inspect(chat_db_engine)
        columns = inspector.get_columns('messages', schema=MARIADB_CHAT_DB)
        has_content = any(col['name'] == 'content' for col in columns)

        if not has_content:
            logger.info("Message 表中没有 content 字段，无需迁移")
            return

        # 获取所有消息
        result = session.execute(text(f"""
            SELECT id, content FROM {MARIADB_CHAT_DB}.messages
            WHERE content IS NOT NULL
        """))

        count = 0
        for row in result:
            message_id = row[0]
            content = row[1]

            if content:
                # 计算内容哈希
                content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()

                # 检查是否已存在
                existing = session.query(MessageContent).filter_by(message_id=message_id).first()
                if existing:
                    # 更新现有记录
                    existing.content = content
                    existing.content_hash = content_hash
                    existing.updated_at = datetime.utcnow()
                else:
                    # 创建新记录
                    new_content = MessageContent(
                        message_id=message_id,
                        content=content,
                        content_hash=content_hash
                    )
                    session.add(new_content)

                count += 1

                # 每1000条提交一次，避免事务过大
                if count % 1000 == 0:
                    session.commit()
                    logger.info(f"已迁移 {count} 条消息内容")

        # 提交剩余的事务
        session.commit()
        logger.info(f"消息内容迁移完成，共迁移 {count} 条记录")

    except Exception as e:
        session.rollback()
        logger.error(f"消息内容迁移失败: {e}")
    finally:
        session.close()


# 在 init_db 函数中添加新表的创建

def init_db():
    # 在模型数据库中创建平台和模型相关表
    Platform.__table__.create(model_db_engine, checkfirst=True)
    AIModel.__table__.create(model_db_engine, checkfirst=True)
    Application.__table__.create(model_db_engine, checkfirst=True)
    AppModel.__table__.create(model_db_engine, checkfirst=True)

    # 在聊天数据库中创建用户、对话和消息相关表，但先不创建外键约束
    # 创建临时表定义，不包含外键
    metadata = MetaData()

    # 为每个表创建没有外键的版本
    tables_to_create = []
    for table_cls in [User, Conversation, Message, Transaction, MessageContent, RenderedContent]:
        # 复制表定义，但不包含外键
        cols = []
        for col in table_cls.__table__.columns:
            # 创建列的副本，但不包含外键
            new_col = Column(col.name, col.type, primary_key=col.primary_key,
                            nullable=col.nullable, unique=col.unique)
            cols.append(new_col)

        # 创建新表定义
        new_table = Table(
            table_cls.__tablename__,
            metadata,
            *cols,
            schema=MARIADB_CHAT_DB
        )
        tables_to_create.append((new_table, table_cls.__table__))

    # 创建所有表，但不包含外键
    for new_table, _ in tables_to_create:
        new_table.create(chat_db_engine, checkfirst=True)

    # 添加外键约束
    with chat_db_engine.begin() as conn:
        for _, original_table in tables_to_create:
            for fkey in original_table.foreign_keys:
                if not fkey.constraint.name:
                    continue
                try:
                    fkey_name = fkey.constraint.name
                    parent_col = fkey.parent.name
                    ref_table = f"{fkey.column.table.schema}.{fkey.column.table.name}"
                    ref_col = fkey.column.name

                    query = text(f"ALTER TABLE {original_table.schema}.{original_table.name} "
                                f"ADD CONSTRAINT {fkey_name} "
                                f"FOREIGN KEY ({parent_col}) "
                                f"REFERENCES {ref_table} ({ref_col})")

                    conn.execute(query)
                    logger.info(f"添加外键约束: {fkey_name}")
                except Exception as e:
                    logger.warning(f"无法添加外键约束 {fkey.constraint.name}: {e}")

    logger.info("数据库表初始化完成")
    
    # 迁移消息内容
    try:
        migrate_message_content()
    except Exception as e:
        logger.error(f"迁移消息内容时出错: {e}")

    logger.info("数据库初始化和迁移完成")



# 如果直接运行此文件，则初始化数据库
if __name__ == "__main__":
    init_db()
