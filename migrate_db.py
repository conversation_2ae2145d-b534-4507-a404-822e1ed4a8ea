# migrate_db.py

import os
import logging
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
import hashlib
import zlib
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MariaDB连接配置
MARIADB_USER = os.environ.get('MARIADB_USER', 'root')
MARIADB_PASSWORD = os.environ.get('MARIADB_PASSWORD', 'rw80827')
MARIADB_HOST = os.environ.get('MARIADB_HOST', '*************')
MARIADB_PORT = os.environ.get('MARIADB_PORT', '3306')
MARIADB_CHAT_DB = os.environ.get('MARIADB_CHAT_DB', 'chat_system')

# 创建MariaDB引擎 - 聊天系统库
chat_db_uri = f'mysql+pymysql://{MARIADB_USER}:{MARIADB_PASSWORD}@{MARIADB_HOST}:{MARIADB_PORT}/{MARIADB_CHAT_DB}'
chat_db_engine = create_engine(
    chat_db_uri,
    pool_size=10,
    max_overflow=15,
    pool_timeout=60,
    pool_pre_ping=True
)

# 创建会话工厂
ChatDBSession = sessionmaker(bind=chat_db_engine)

def check_table_exists(table_name, schema):
    """检查表是否存在"""
    inspector = inspect(chat_db_engine)
    return table_name in inspector.get_table_names(schema=schema)

def check_column_exists(table_name, column_name, schema):
    """检查列是否存在"""
    inspector = inspect(chat_db_engine)
    columns = inspector.get_columns(table_name, schema=schema)
    return any(col['name'] == column_name for col in columns)

def create_message_content_table():
    """创建消息内容表"""
    if check_table_exists('message_contents', MARIADB_CHAT_DB):
        logger.info("message_contents 表已存在")
        return

    logger.info("创建 message_contents 表")
    with chat_db_engine.begin() as conn:
        conn.execute(text(f"""
            CREATE TABLE {MARIADB_CHAT_DB}.message_contents (
                id INT AUTO_INCREMENT PRIMARY KEY,
                message_id INT NOT NULL,
                content MEDIUMTEXT,
                content_hash VARCHAR(64),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY (message_id),
                CONSTRAINT fk_message_content_message
                    FOREIGN KEY (message_id)
                    REFERENCES {MARIADB_CHAT_DB}.messages(id)
                    ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """))

def create_rendered_content_table():
    """创建渲染内容表"""
    if check_table_exists('rendered_contents', MARIADB_CHAT_DB):
        logger.info("rendered_contents 表已存在")
        return

    logger.info("创建 rendered_contents 表")
    with chat_db_engine.begin() as conn:
        conn.execute(text(f"""
            CREATE TABLE {MARIADB_CHAT_DB}.rendered_contents (
                id INT AUTO_INCREMENT PRIMARY KEY,
                message_id INT NOT NULL,
                rendered_with_mathjax MEDIUMBLOB,
                rendered_without_mathjax MEDIUMBLOB,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY (message_id),
                CONSTRAINT fk_rendered_content_message
                    FOREIGN KEY (message_id)
                    REFERENCES {MARIADB_CHAT_DB}.messages(id)
                    ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """))

def migrate_message_content():
    """将消息内容从 messages 表迁移到 message_contents 表"""
    # 检查 messages 表是否有 content 列
    if not check_column_exists('messages', 'content', MARIADB_CHAT_DB):
        logger.info("messages 表中没有 content 列，无需迁移")
        return

    # 检查 message_contents 表是否存在
    if not check_table_exists('message_contents', MARIADB_CHAT_DB):
        logger.error("message_contents 表不存在，请先创建表")
        return

    logger.info("开始迁移消息内容")
    session = ChatDBSession()
    try:
        # 获取所有消息
        result = session.execute(text(f"""
            SELECT id, content FROM {MARIADB_CHAT_DB}.messages
            WHERE content IS NOT NULL
        """))

        count = 0
        for row in result:
            message_id = row[0]
            content = row[1]

            if content:
                # 计算内容哈希
                content_hash = hashlib.sha256(content.encode('utf-8')).hexdigest()

                # 检查是否已存在
                existing = session.execute(text(f"""
                    SELECT id FROM {MARIADB_CHAT_DB}.message_contents
                    WHERE message_id = :message_id
                """), {"message_id": message_id}).fetchone()

                if existing:
                    # 更新现有记录
                    session.execute(text(f"""
                        UPDATE {MARIADB_CHAT_DB}.message_contents
                        SET content = :content, content_hash = :content_hash, updated_at = NOW()
                        WHERE message_id = :message_id
                    """), {"content": content, "content_hash": content_hash, "message_id": message_id})
                else:
                    # 创建新记录
                    session.execute(text(f"""
                        INSERT INTO {MARIADB_CHAT_DB}.message_contents
                        (message_id, content, content_hash, created_at, updated_at)
                        VALUES (:message_id, :content, :content_hash, NOW(), NOW())
                    """), {"message_id": message_id, "content": content, "content_hash": content_hash})

                count += 1

                # 每1000条提交一次，避免事务过大
                if count % 1000 == 0:
                    session.commit()
                    logger.info(f"已迁移 {count} 条消息内容")

        # 提交剩余的事务
        session.commit()
        logger.info(f"消息内容迁移完成，共迁移 {count} 条记录")

    except Exception as e:
        session.rollback()
        logger.error(f"消息内容迁移失败: {e}")
    finally:
        session.close()

def drop_content_column():
    """从 messages 表中删除 content 列"""
    # 检查 messages 表是否有 content 列
    if not check_column_exists('messages', 'content', MARIADB_CHAT_DB):
        logger.info("messages 表中已经没有 content 列")
        return

    # 确保所有内容已迁移
    session = ChatDBSession()
    try:
        # 检查是否有未迁移的内容
        result = session.execute(text(f"""
            SELECT COUNT(*) FROM {MARIADB_CHAT_DB}.messages m
            LEFT JOIN {MARIADB_CHAT_DB}.message_contents mc ON m.id = mc.message_id
            WHERE m.content IS NOT NULL AND mc.id IS NULL
        """)).fetchone()

        if result and result[0] > 0:
            logger.warning(f"还有 {result[0]} 条消息内容未迁移，请先完成迁移")
            return

        # 删除 content 列
        logger.info("从 messages 表中删除 content 列")
        with chat_db_engine.begin() as conn:
            conn.execute(text(f"""
                ALTER TABLE {MARIADB_CHAT_DB}.messages
                DROP COLUMN content
            """))

        logger.info("content 列已成功删除")

    except Exception as e:
        logger.error(f"删除 content 列失败: {e}")
    finally:
        session.close()

def main():
    """主函数"""
    try:
        # 1. 创建新表
        create_message_content_table()
        create_rendered_content_table()

        # 2. 迁移数据
        migrate_message_content()

        # 3. 询问是否删除原始列
        answer = input("是否从 messages 表中删除 content 列？(y/n): ")
        if answer.lower() == 'y':
            drop_content_column()
        else:
            logger.info("保留 content 列")

        logger.info("数据库迁移完成")

    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")

if __name__ == "__main__":
    main()
