# openaiSDK.py (修改版)
import logging
import json
import requests
from typing import List, Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenAISDK:
    API_BASE_URL = "http://localhost:5002/api/v1"
    
    @staticmethod
    def extract_text_from_multimodal(content):
        """从多模态内容中提取纯文本"""
        try:
            if isinstance(content, str):
                try:
                    content_obj = json.loads(content)
                except json.JSONDecodeError:
                    # 不是JSON格式，直接返回原始文本
                    return content
            else:
                content_obj = content

            if isinstance(content_obj, list):
                texts = []
                for item in content_obj:
                    if item.get("type") == "text":
                        texts.append(item.get("text", ""))
                return " ".join(texts)

            return content
        except Exception as e:
            logger.error(f"提取文本内容出错: {e}")
            # 如果处理出错，返回原始内容
            return content if isinstance(content, str) else str(content)

    @staticmethod
    def process_content_for_api(content, role, supports_vision=True):
        """
        处理消息内容，根据角色和模型能力调整格式

        Args:
            content: 消息内容（字符串或JSON字符串）
            role: 消息角色 (user, assistant, system)
            supports_vision: 模型是否支持图片输入
        """
        try:
            # assistant 和 system 角色消息始终使用纯文本格式
            if role in ["assistant", "system"]:
                # 如果内容已经是字符串，直接返回
                if isinstance(content, str):
                    # 检查是否为JSON格式的存储内容 [{"type": "text", "text": "..."}]
                    try:
                        content_obj = json.loads(content)
                        if isinstance(content_obj, list) and content_obj and content_obj[0].get("type") == "text":
                            # 提取并返回文本内容
                            return content_obj[0].get("text", "")
                    except json.JSONDecodeError:
                        # 不是JSON格式，直接返回原始文本
                        return content
                else:
                    # 非字符串内容转为字符串
                    try:
                        return str(content)
                    except:
                        return "无法解析的内容"

            # 以下处理用户消息
            # 如果模型不支持图片，直接提取纯文本
            if not supports_vision:
                return OpenAISDK.extract_text_from_multimodal(content)

            # 模型支持图片，处理为多模态格式
            if isinstance(content, str):
                try:
                    content_obj = json.loads(content)
                except json.JSONDecodeError:
                    # 不是JSON格式，将纯文本转换为新格式
                    return [{"type": "text", "text": content}]
            else:
                content_obj = content

            # 如果已经是多模态列表格式
            if isinstance(content_obj, list):
                processed_content = []
                for item in content_obj:
                    # 处理文本项
                    if item.get("type") == "text":
                        processed_content.append(item)
                    # 处理图片URL项 - 这里图片已经是base64格式，不需要转换
                    elif item.get("type") == "image_url" and "image_url" in item:
                        processed_content.append(item)
                return processed_content
            else:
                # 如果是其他格式，转换为文本
                return [{"type": "text", "text": str(content_obj)}]

        except Exception as e:
            logger.error(f"处理内容异常: {e}, role={role}")
            # 根据角色决定返回格式
            if role == "assistant":
                return str(content) if isinstance(content, str) else "处理内容时出错"
            elif supports_vision:
                return [{"type": "text", "text": str(content)}]
            else:
                return str(content)

    def __init__(self, user_id=None, api_key="sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo"):
        self.user_id = user_id
        self.api_key = api_key
        self.conversation_id = None
        self.current_message_id = None
        self.supports_vision = False
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 获取可用模型信息
        self.available_models = self._get_available_models()
        
        # 如果提供了用户ID，获取用户数据
        if self.user_id:
            self.load_user_data()

    def _get_available_models(self):
        """获取API服务支持的模型列表"""
        try:
            response = requests.get(
                f"{self.API_BASE_URL}/models", 
                headers=self.headers
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("models", [])
            else:
                logger.error(f"获取模型列表失败: {response.status_code} - {response.text}")
                return []
        except Exception as e:
            logger.error(f"获取模型列表异常: {e}")
            return []

    def load_user_data(self):
        """加载用户数据和当前设置"""
        try:
            from db_operator import get_user_data
            
            self.user_data = get_user_data(self.user_id)
            if not self.user_data:
                raise ValueError(f"未找到用户ID: {self.user_id}")

            # 获取用户当前对话
            self.conversation_id = self.user_data.get("current_conversation_id")
            if not self.conversation_id:
                raise ValueError(f"用户没有当前对话")

            # 获取用户当前模型
            model_id = self.user_data.get("current_model_id")
            if not model_id:
                raise ValueError(f"用户没有设置当前模型")

            # 检查模型是否在可用模型列表中
            model_info = next((m for m in self.available_models if str(m["id"]) == str(model_id)), None)
            if model_info:
                self.model_name = model_info["name"]
                # 假设支持vision的模型名称包含特定标记，如gpt-4-vision
                self.supports_vision = "vision" in self.model_name.lower()
                logger.info(f"当前模型 {self.model_name} 支持图片输入: {self.supports_vision}")
            else:
                # 使用默认模型
                default_model = next((m for m in self.available_models if m.get("is_default")), None)
                if default_model:
                    self.model_name = default_model["name"]
                    logger.info(f"使用默认模型: {self.model_name}")
                else:
                    raise ValueError("无法确定要使用的模型")

        except Exception as e:
            logger.error(f"加载用户数据失败: {e}")
            raise

    def generate_response_sync(self) -> str:
        """同步生成AI响应，支持多模态内容，并直接保存到数据库"""
        if not self.conversation_id:
            raise ValueError("未指定对话ID")

        try:
            # 获取历史消息
            from db_operator import load_conversation_messages_by_id, create_message, get_conversation_messages_list
            
            db_messages = load_conversation_messages_by_id(self.conversation_id)
            if not db_messages:
                raise ValueError(f"对话 {self.conversation_id} 没有消息")

            # 获取当前模型ID
            model_id = self.user_data.get("current_model_id")
            if not model_id:
                raise ValueError("未找到有效的模型ID")

            # 获取模型内部名称
            model_data = get_model_data(model_id)
            if not model_data:
                raise ValueError(f"未找到模型ID: {model_id}")

            model_name = model_data.get("internal_name")
            temperature = float(self.user_data.get("current_temperature", 0.7))

            # 确定模型是否支持图片输入
            supports_vision = model_data.get("is_visible_model", False)

            api_messages = []
            for msg in db_messages:
                role = msg["role"]
                content = msg["content"]

                # 根据模型能力和角色处理内容
                processed_content = self.process_content_for_api(
                    content,
                    role=role,
                    supports_vision=self.supports_vision
                )

                api_messages.append({
                    "role": role,
                    "content": processed_content
                })

            # 记录发送到API的消息
            logger.info(f"发送到API的消息数量: {len(api_messages)}")
            logger.info(f"模型 {self.model_name} 支持图片输入: {self.supports_vision}")

            # 使用新的API服务发送请求
            payload = {
                "model": self.model_name,
                "messages": api_messages,
                "temperature": temperature,
                "stream": False
            }
            
            response = requests.post(
                f"{self.API_BASE_URL}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=180
            )
            
            if response.status_code != 200:
                raise ValueError(f"API请求失败: {response.status_code} - {response.text}")
            
            # 处理响应
            response_data = response.json()
            ai_message = response_data["choices"][0]["message"]["content"]
            prompt_tokens = response_data["usage"]["prompt_tokens"]
            completion_tokens = response_data["usage"]["completion_tokens"]

            # 获取最新添加的消息ID（假设是用户消息）
            messages_list = get_conversation_messages_list(self.conversation_id)

            if messages_list and len(messages_list) > 0:
                # 找到最后一条用户消息
                for msg in reversed(messages_list):
                    if msg["role"] == "user":
                        self.current_message_id = msg["id"]
                        break

            # 创建AI响应内容
            ai_response_content = []
            ai_response_content.append({
                "type": "text",
                "text": ai_message
            })

            # 获取当前模型ID
            from db_operator import get_user_model_id
            model_id = get_user_model_id(self.user_id)

            # 直接将AI响应添加到数据库
            create_message(
                conversation_id=self.conversation_id,
                role="assistant",
                content=json.dumps(ai_response_content, ensure_ascii=False),
                model_id=model_id,
                temperature=temperature,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens
            )

            return ai_message

        except Exception as e:
            logger.error(f"生成响应失败: {e}")
            # 当发生异常时，也将错误消息保存到数据库
            error_message = f"抱歉，我遇到了一个问题：{str(e)}"
            error_content = json.dumps([{"type": "text", "text": error_message}], ensure_ascii=False)
            
            from db_operator import get_user_model_id, create_message
            model_id = get_user_model_id(self.user_id)

            create_message(
                conversation_id=self.conversation_id,
                role="assistant",
                content=error_content,
                model_id=model_id if model_id else None,
                temperature=temperature if 'temperature' in locals() else None,
                is_error=True,
                error_info=str(e)
            )

            return error_message
