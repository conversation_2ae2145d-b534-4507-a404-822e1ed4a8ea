"""
应用主题和样式常量定义
"""

# 颜色定义
COLORS = {
    'primary': 'pink',  #'#0d6efd',       # 主要颜色
    'secondary': '#6c757d',     # 次要颜色
    'success': '#198754',       # 成功颜色
    'danger': '#dc3545',        # 危险/错误颜色
    'warning': '#ffc107',       # 警告颜色
    'info': '#0dcaf0',          # 信息颜色
    'light': '#f8f9fa',         # 浅色
    'dark': '#212529',          # 深色
    'white': '#ffffff',         # 白色
    'transparent': 'transparent', # 透明
}

# 尺寸常量
SIZES = {
    'navbar_height': '64px',
    'sidebar_width': '300px',
    'content_max_width': '1200px',
    'message_container_height': '500px',
    'message_input_height': '120px',
}

# 移动设备布局常量
MOBILE_BREAKPOINT = '768px'
MOBILE_CONTROL_WIDTH = '150px'

# 布局间距
SPACING = {
    'xs': '0.25rem',
    'sm': '0.5rem',
    'md': '1rem',
    'lg': '1.5rem',
    'xl': '3rem',
}

# 字体设置
FONTS = {
    'primary': '"Roboto", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
    'monospace': 'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
}

# 圆角样式
BORDER_RADIUS = {
    'sm': '0.2rem',
    'md': '0.375rem',
    'lg': '0.5rem',
    'xl': '1rem',
    'circle': '50%',
}

# 动画时间
ANIMATION = {
    'fast': '0.15s',
    'normal': '0.3s',
    'slow': '0.5s',
}
