# utils/image_converter.py
import io
from PIL import Image
import logging

logger = logging.getLogger(__name__)


def convert_bmp_to_png(image_data):
    """
    将BMP格式的图像数据转换为PNG格式

    Args:
        image_data (bytes): BMP图像的二进制数据

    Returns:
        tuple: (转换后的PNG图像数据, 新的content_type)
    """
    try:
        # 使用PIL库打开图像数据
        img = Image.open(io.BytesIO(image_data))

        # 创建一个BytesIO对象来存储转换后的PNG图像
        output = io.BytesIO()

        # 使用PNG格式保存图像
        img.save(output, format='PNG')

        # 获取转换后的图像数据
        png_data = output.getvalue()

        # 返回PNG图像数据和content_type
        return png_data, "image/png"

    except Exception as e:
        logger.error(f"BMP转PNG失败: {e}")
        # 转换失败，返回原始数据
        return image_data, "image/bmp"
