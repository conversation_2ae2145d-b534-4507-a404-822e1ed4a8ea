import base64
import io
import logging
import os
from PIL import Image

logger = logging.getLogger(__name__)

def create_thumbnail(image_data, max_size=(100, 100)):
    """
    创建图片缩略图

    Args:
        image_data (bytes): 图片的二进制数据
        max_size (tuple): 缩略图最大尺寸

    Returns:
        bytes: 缩略图的二进制数据
    """
    try:
        img = Image.open(io.BytesIO(image_data))
        img.thumbnail(max_size)
        output = io.BytesIO()

        # 保存为JPEG格式的缩略图
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        img.save(output, format='JPEG', quality=70)
        output.seek(0)

        return output.getvalue()
    except Exception as e:
        logger.error(f"创建缩略图失败: {e}")
        return None

def process_image_to_base64(image_data, content_type=None, filename=None):
    """
    处理图片数据，转换为base64格式
    注意：BMP转PNG的处理已经在前端完成

    Args:
        image_data (bytes): 图片的二进制数据
        content_type (str, optional): 图片的MIME类型
        filename (str, optional): 图片文件名，用于确定格式

    Returns:
        str: base64格式的数据URL
    """
    try:
        # 如果提供了文件名但没有content_type，尝试从文件名确定类型
        if filename and not content_type:
            file_ext = os.path.splitext(filename)[1].lower()
            if file_ext == ".png":
                content_type = "image/png"
            elif file_ext in [".jpg", ".jpeg"]:
                content_type = "image/jpeg"
            elif file_ext == ".gif":
                content_type = "image/gif"
            elif file_ext == ".bmp":
                content_type = "image/bmp"
            else:
                # 默认为JPEG
                content_type = "image/jpeg"

        # 转换为base64
        base64_data = base64.b64encode(image_data).decode('utf-8')

        # 构建数据URL
        data_url = f"data:{content_type};base64,{base64_data}"
        logger.info(f"图片成功转换为base64格式，大小: {len(image_data)} 字节")

        return data_url

    except Exception as e:
        logger.error(f"图片处理失败: {e}")
        return None
