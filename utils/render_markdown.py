from dash import html
import markdown
import re
import uuid
import json

def render_content(text, detect_markdown=True, mathjax=True):
    """
    使用iframe隔离环境来渲染Markdown和LaTeX

    参数:
    text (str): 要渲染的文本
    detect_markdown (bool): 是否自动检测Markdown格式
    mathjax (bool): 是否渲染LaTeX公式

    返回:
    dash 组件
    """

    # 预处理文本，保护代码块并替换LaTeX分隔符
    def preprocess_text(text):
        # 保存代码块的字典，使用唯一标识符作为键
        code_blocks = {}

        # 首先匹配和保存三重反引号代码块
        fenced_pattern = r'(```[^\n]*\n[\s\S]*?```)'

        def replace_code_block(match):
            block = match.group(1)
            placeholder = f"CODE_BLOCK_{str(uuid.uuid4())}"
            code_blocks[placeholder] = block
            return placeholder

        text_processed = re.sub(fenced_pattern, replace_code_block, text)

        # 如果启用mathjax，替换LaTeX分隔符
        if mathjax:
            # 处理 \[ ... \] -> $$ ... $$
            text_processed = re.sub(r'\\\[([\s\S]*?)\\\]', r'$$ \1 $$', text_processed)
            # 处理 \( ... \) -> $ ... $
            text_processed = re.sub(r'\\\(([\s\S]*?)\\\)', r'$ \1 $', text_processed)

        # 恢复代码块
        for placeholder, block in code_blocks.items():
            text_processed = text_processed.replace(placeholder, block)

        return text_processed

    # 如果需要检测并处理Markdown
    if detect_markdown:
        processed_text = preprocess_text(text)
        # 转换Markdown为HTML
        html_content = markdown.markdown(
            processed_text,
            extensions=[
                'extra',
                'codehilite',
                'fenced_code',
                'tables'
            ]
        )
    else:
        html_content = f"<pre>{text}</pre>"

    # MathJax配置和CSS
    head_content = """
    <head>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.6;
                padding: 15px;
                max-width: 100%;
                overflow-x: hidden;
                margin: 0;
            }
            pre { 
                background-color: #f5f5f5; 
                padding: 10px; 
                border-radius: 3px; 
                overflow-x: auto;
            }
            code {
                font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
                background-color: #f5f5f5;
                border-radius: 3px;
                padding: 2px 4px;
            }
            img { max-width: 100%; }
            table { 
                border-collapse: collapse; 
                width: 100%; 
                margin-bottom: 15px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            blockquote {
                margin-left: 0;
                padding-left: 10px;
                border-left: 3px solid #ddd;
                color: #555;
            }
            /* 减少内容的上下边距，使文本更紧凑 */
            p {
                margin-top: 0.5em;
                margin-bottom: 0.5em;
            }
            /* 针对多级标题的边距调整 */
            h1, h2, h3, h4, h5, h6 {
                margin-top: 0.8em;
                margin-bottom: 0.5em;
            }
        </style>
    """

    # 仅当mathjax=True时才添加MathJax脚本
    if mathjax:
        head_content += """
        <script type="text/x-mathjax-config">
            MathJax.Hub.Config({
                tex2jax: {
                    inlineMath: [['$','$'], ['\\\\(','\\\\)']],
                    displayMath: [['$$','$$'], ['\\\\[','\\\\]']],
                    processEscapes: true,
                    processEnvironments: true
                },
                displayAlign: "center",
                "HTML-CSS": { 
                    styles: {'.MathJax_Display': {"margin": "0.5em 0"}},
                    linebreaks: { automatic: true }
                }
            });
        </script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML" async></script>
        """

    head_content += """
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', (event) => {
                document.querySelectorAll('pre code').forEach((el) => {
                    hljs.highlightElement(el);
                });
            });
        </script>
    </head>
    """

    script_content = """
        <script>
    // 使用节流确保高度消息不会频繁发送
    let lastHeight = 0;
    let lastSentTime = 0;
    let resizeTimeout = null;
    
    // 计算并发送高度 - 只有当高度真正变化时
    function sendHeight() {
        try {
            const body = document.body;
            const html = document.documentElement;
            
            // 计算当前高度
            const currentHeight = Math.max(
                body.scrollHeight, 
                body.offsetHeight,
                html.clientHeight, 
                html.scrollHeight, 
                html.offsetHeight
            );
            
            // 只有当高度真正变化时才发送消息
            if (Math.abs(currentHeight - lastHeight) > 5) {
                lastHeight = currentHeight;
                lastSentTime = Date.now();
                
                // 发送高度消息
                window.parent.postMessage({
                    height: currentHeight-15,
                    iframe_id: window.name
                }, '*');
            }
        } catch (e) {
            console.error("计算高度出错:", e);
        }
    }

    // 使用节流控制调整频率
    function throttledResize() {
        // 清除任何待处理的超时
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
            resizeTimeout = null;
        }
        
        // 执行即时调整
        sendHeight();
        
        // 确保一段时间内不会再次调用
        const now = Date.now();
        if (now - lastSentTime > 1000) {  // 限制为最多每秒调整一次
            // 设置延迟检查以捕获最终状态
            resizeTimeout = setTimeout(sendHeight, 500);
        }
    }
    
    // 有限的初始化调用
    document.addEventListener('DOMContentLoaded', throttledResize);
    
    // 一次性的加载调用
    let loadHandled = false;
    window.addEventListener('load', function() {
        if (!loadHandled) {
            loadHandled = true;
            throttledResize();
            // 最终检查
            setTimeout(throttledResize, 500);
        }
    });
    
    // 响应父窗口调整请求 - 使用节流
    window.addEventListener('message', function(e) {
        if (e.data && e.data.action === 'resize') {
            throttledResize();
        }
    });
    
    // MathJax支持
    if (typeof MathJax !== 'undefined') {
        MathJax.Hub.Queue(throttledResize);
    }
</script>

        """

    # 构建完整HTML文档
    full_html = f"""<!DOCTYPE html>
        <html>
        {head_content}
        <body>
        {html_content}
        {script_content}
        </body>
        </html>"""

    # 生成唯一ID用于iframe
    iframe_id = f"markdown-iframe-{str(uuid.uuid4())[:8]}"

    # 返回iframe组件部分修改
    return html.Iframe(
        srcDoc=full_html,
        style={
            "width": "100%",
            "height": "40px",  # 使用较小的初始高度
            "border": "none",
            "overflow": "hidden",
            "display": "block",
            "transition": "height 0.1s"  # 平滑但快速的过渡
        },
        id=iframe_id,
        name=iframe_id,
        className="rendered-markdown-frame",
        sandbox="allow-scripts"  # 确保脚本可执行但增加安全性
    )

# 在 utils/render_markdown.py 中添加新函数

def render_content_to_html(text, detect_markdown=True, mathjax=True):
    """
    将内容渲染为HTML字符串

    参数:
    text (str): 要渲染的文本
    detect_markdown (bool): 是否自动检测Markdown格式
    mathjax (bool): 是否渲染LaTeX公式

    返回:
    str: 渲染后的HTML内容
    """
    # 尝试解析JSON格式的多模态内容
    try:
        content_objects = json.loads(text)
        if isinstance(content_objects, list):
            # 处理多模态内容
            html_parts = []
            for item in content_objects:
                item_type = item.get("type")
                if item_type == "text":
                    # 处理文本
                    text_content = item.get("text", "")
                    if detect_markdown:
                        processed_text = preprocess_text(text_content, mathjax)
                        html_part = markdown.markdown(
                            processed_text,
                            extensions=['extra', 'codehilite', 'fenced_code', 'tables']
                        )
                    else:
                        html_part = f"<pre>{text_content}</pre>"
                    html_parts.append(html_part)
                elif item_type == "image_url":
                    # 处理图片URL
                    image_url = item.get("image_url", {}).get("url", "")
                    if image_url:
                        html_parts.append(f'<img src="{image_url}" class="message-image" style="max-width: 70%; max-height: 300px; width: auto; height: auto;">')

            # 合并所有HTML部分
            html_content = "".join(html_parts)
        else:
            # 不是列表格式，按普通文本处理
            if detect_markdown:
                processed_text = preprocess_text(text, mathjax)
                html_content = markdown.markdown(
                    processed_text,
                    extensions=['extra', 'codehilite', 'fenced_code', 'tables']
                )
            else:
                html_content = f"<pre>{text}</pre>"
    except (json.JSONDecodeError, TypeError):
        # 不是JSON格式，按普通文本处理
        if detect_markdown:
            processed_text = preprocess_text(text, mathjax)
            html_content = markdown.markdown(
                processed_text,
                extensions=['extra', 'codehilite', 'fenced_code', 'tables']
            )
        else:
            html_content = f"<pre>{text}</pre>"

    # 构建完整的HTML文档
    head_content = """
    <head>
        <style>
            body { 
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                line-height: 1.6;
                padding: 15px;
                max-width: 100%;
                overflow-x: hidden;
                margin: 0;
            }
            pre { 
                background-color: #f5f5f5; 
                padding: 10px; 
                border-radius: 3px; 
                overflow-x: auto;
            }
            code {
                font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
                background-color: #f5f5f5;
                border-radius: 3px;
                padding: 2px 4px;
            }
            img { max-width: 100%; }
            table { 
                border-collapse: collapse; 
                width: 100%; 
                margin-bottom: 15px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            blockquote {
                margin-left: 0;
                padding-left: 10px;
                border-left: 3px solid #ddd;
                color: #555;
            }
            p {
                margin-top: 0.5em;
                margin-bottom: 0.5em;
            }
            h1, h2, h3, h4, h5, h6 {
                margin-top: 0.8em;
                margin-bottom: 0.5em;
            }
        </style>
    """

    # 仅当mathjax=True时才添加MathJax脚本
    if mathjax:
        head_content += """
        <script type="text/x-mathjax-config">
            MathJax.Hub.Config({
                tex2jax: {
                    inlineMath: [['$','$'], ['\\\\(','\\\\)']],
                    displayMath: [['$$','$$'], ['\\\\[','\\\\]']],
                    processEscapes: true,
                    processEnvironments: true
                },
                displayAlign: "center",
                "HTML-CSS": { 
                    styles: {'.MathJax_Display': {"margin": "0.5em 0"}},
                    linebreaks: { automatic: true }
                }
            });
        </script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML" async></script>
        """

    head_content += """
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', (event) => {
                document.querySelectorAll('pre code').forEach((el) => {
                    hljs.highlightElement(el);
                });
            });
        </script>
    </head>
    """

    script_content = """
        <script>
    // 使用节流确保高度消息不会频繁发送
    let lastHeight = 0;
    let lastSentTime = 0;
    let resizeTimeout = null;

    // 计算并发送高度 - 只有当高度真正变化时
    function sendHeight() {
        try {
            const body = document.body;
            const html = document.documentElement;

            // 计算当前高度
            const currentHeight = Math.max(
                body.scrollHeight, 
                body.offsetHeight,
                html.clientHeight, 
                html.scrollHeight, 
                html.offsetHeight
            );

            // 只有当高度真正变化时才发送消息
            if (Math.abs(currentHeight - lastHeight) > 5) {
                lastHeight = currentHeight;
                lastSentTime = Date.now();

                // 发送高度消息
                window.parent.postMessage({
                    height: currentHeight-15,
                    iframe_id: window.name
                }, '*');
            }
        } catch (e) {
            console.error("计算高度出错:", e);
        }
    }

    // 使用节流控制调整频率
    function throttledResize() {
        // 清除任何待处理的超时
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
            resizeTimeout = null;
        }

        // 执行即时调整
        sendHeight();

        // 确保一段时间内不会再次调用
        const now = Date.now();
        if (now - lastSentTime > 1000) {  // 限制为最多每秒调整一次
            // 设置延迟检查以捕获最终状态
            resizeTimeout = setTimeout(sendHeight, 500);
        }
    }

    // 有限的初始化调用
    document.addEventListener('DOMContentLoaded', throttledResize);

    // 一次性的加载调用
    let loadHandled = false;
    window.addEventListener('load', function() {
        if (!loadHandled) {
            loadHandled = true;
            throttledResize();
            // 最终检查
            setTimeout(throttledResize, 500);
        }
    });

    // 响应父窗口调整请求 - 使用节流
    window.addEventListener('message', function(e) {
        if (e.data && e.data.action === 'resize') {
            throttledResize();
        }
    });

    // MathJax支持
    if (typeof MathJax !== 'undefined') {
        MathJax.Hub.Queue(throttledResize);
    }
</script>
        """

    # 构建完整HTML文档
    full_html = f"""<!DOCTYPE html>
        <html>
        {head_content}
        <body>
        {html_content}
        {script_content}
        </body>
        </html>"""

    return full_html

# 辅助函数：预处理文本
def preprocess_text(text, mathjax=True):
    """预处理文本，保护代码块并替换LaTeX分隔符"""
    # 保存代码块的字典，使用唯一标识符作为键
    code_blocks = {}

    # 首先匹配和保存三重反引号代码块
    fenced_pattern = r'(```[^\n]*\n[\s\S]*?```)'

    def replace_code_block(match):
        block = match.group(1)
        placeholder = f"CODE_BLOCK_{str(uuid.uuid4())}"
        code_blocks[placeholder] = block
        return placeholder

    text_processed = re.sub(fenced_pattern, replace_code_block, text)

    # 如果启用mathjax，替换LaTeX分隔符
    if mathjax:
        # 处理 \[ ... \] -> $$ ... $$
        text_processed = re.sub(r'\\\[([\s\S]*?)\\\]', r'$$ \1 $$', text_processed)
        # 处理 \( ... \) -> $ ... $
        text_processed = re.sub(r'\\\(([\s\S]*?)\\\)', r'$ \1 $', text_processed)

    # 恢复代码块
    for placeholder, block in code_blocks.items():
        text_processed = text_processed.replace(placeholder, block)

    return text_processed
